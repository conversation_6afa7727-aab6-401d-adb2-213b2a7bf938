<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  HomeIcon,
  KeyIcon,
  CodeBracketIcon,
  UserIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()

const navigation = [
  {
    name: '控制台',
    href: '/dashboard',
    icon: HomeIcon,
    current: computed(() => route.path === '/dashboard')
  },
  {
    name: '开发者中心',
    href: '/developer',
    icon: CodeBracketIcon,
    current: computed(() => route.path.startsWith('/developer')),
    children: [
      {
        name: '接口列表',
        href: '/developer/apis',
        current: computed(() => route.path === '/developer/apis')
      },
      {
        name: '提交接口',
        href: '/developer/submit',
        current: computed(() => route.path === '/developer/submit')
      }
    ]
  },
  {
    name: '个人设置',
    href: '/profile',
    icon: Cog6ToothIcon,
    current: computed(() => route.path.startsWith('/profile')),
    children: [
      {
        name: '个人信息',
        href: '/profile/info',
        current: computed(() => route.path === '/profile/info')
      },
      {
        name: '安全设置',
        href: '/profile/security',
        current: computed(() => route.path === '/profile/security')
      }
    ]
  }
]
</script>

<template>
  <div class="flex flex-col w-64 bg-white shadow-sm border-r border-gray-200">
    <div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
      <div class="flex items-center flex-shrink-0 px-4">
        <h2 class="text-lg font-medium text-gray-900">用户控制台</h2>
      </div>
      <nav class="mt-5 flex-1 px-2 space-y-1">
        <template v-for="item in navigation" :key="item.name">
          <div>
            <router-link
              :to="item.href"
              :class="[
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                item.current.value
                  ? 'bg-primary-100 text-primary-900'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <component
                :is="item.icon"
                :class="[
                  'mr-3 flex-shrink-0 h-5 w-5',
                  item.current.value
                    ? 'text-primary-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
              {{ item.name }}
            </router-link>
            
            <!-- 子菜单 -->
            <div v-if="item.children && item.current.value" class="mt-1 space-y-1">
              <router-link
                v-for="child in item.children"
                :key="child.name"
                :to="child.href"
                :class="[
                  'group flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md transition-colors',
                  child.current.value
                    ? 'bg-primary-50 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                ]"
              >
                {{ child.name }}
              </router-link>
            </div>
          </div>
        </template>
      </nav>
    </div>
  </div>
</template>

<style scoped>
/* 控制台侧边栏样式 */
</style>
