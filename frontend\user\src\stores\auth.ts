// Copyright (c) 2025 Aoki. All rights reserved.

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { authApi } from '@/api/auth'
import type { User, LoginCredentials, RegisterData } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const toast = useToast()
  
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  // 安全地从localStorage获取数据
  const initializeFromStorage = () => {
    try {
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')

      if (storedToken) {
        token.value = storedToken
      }

      if (storedUser && storedUser !== 'undefined' && storedUser !== 'null') {
        user.value = JSON.parse(storedUser)
      }
    } catch (error) {
      console.warn('Failed to parse stored user data:', error)
      // 清除损坏的数据
      localStorage.removeItem('user')
      localStorage.removeItem('token')
    }
  }

  // 初始化
  initializeFromStorage()
  const loading = ref(false)
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  
  // 登录
  const login = async (credentials: LoginCredentials) => {
    try {
      loading.value = true
      console.log('Attempting login with:', { email: credentials.email })

      const response = await authApi.login(credentials)
      console.log('Login response:', response)

      if (response.success && response.data) {
        const { token: authToken, user: userData } = response.data

        if (authToken && userData) {
          token.value = authToken
          localStorage.setItem('token', authToken)

          console.log('Login successful, token saved:', authToken.substring(0, 20) + '...')

          // 获取完整的用户信息（包括avatar_url）
          await fetchUser()
        } else {
          console.error('Missing token or user data in response:', response.data)
          toast.error('登录响应数据不完整')
          return
        }

        toast.success('登录成功')

        // 重定向到目标页面或控制台
        const redirect = router.currentRoute.value.query.redirect as string
        const targetRoute = redirect || '/dashboard'
        console.log('Redirecting to:', targetRoute)

        await router.push(targetRoute)
      } else {
        console.error('Login failed:', response)
        toast.error(response.message || '登录失败')
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // 详细的错误处理
      let errorMessage = '登录失败，请稍后重试'

      if (error.response) {
        // 服务器响应错误
        const status = error.response.status
        const data = error.response.data

        switch (status) {
          case 401:
            errorMessage = data?.message || '邮箱或密码错误'
            break
          case 422:
            errorMessage = data?.message || '输入数据格式错误'
            break
          case 429:
            errorMessage = '请求过于频繁，请稍后再试'
            break
          case 500:
            errorMessage = '服务器内部错误，请稍后重试'
            break
          default:
            errorMessage = data?.message || `请求失败 (${status})`
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = '网络连接失败，请检查网络设置'
      } else {
        // 其他错误
        errorMessage = error.message || '未知错误'
      }

      toast.error(errorMessage)
    } finally {
      loading.value = false
    }
  }
  
  // 注册
  const register = async (data: RegisterData) => {
    try {
      loading.value = true
      const response = await authApi.register(data)
      
      if (response.success) {
        toast.success('注册成功，请登录')
        router.push('/auth/login')
      } else {
        toast.error(response.message || '注册失败')
      }
    } catch (error: any) {
      console.error('Register error:', error)
      toast.error(error.response?.data?.message || '注册失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }
  
  // 注销
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      router.push('/')
      toast.success('已退出登录')
    }
  }
  
  // 验证token是否有效
  const validateToken = (tokenString: string): boolean => {
    if (!tokenString) return false

    try {
      // 检查token格式（Laravel Sanctum格式：id|token）
      const parts = tokenString.split('|')
      if (parts.length !== 2) return false

      // 检查token长度
      if (parts[1].length < 40) return false

      return true
    } catch (error) {
      console.error('Token validation error:', error)
      return false
    }
  }

  // 获取用户信息
  const fetchUser = async () => {
    try {
      if (!token.value || !validateToken(token.value)) {
        console.warn('Invalid token, logging out')
        logout()
        return
      }

      const response = await authApi.me()
      if (response.success && response.data) {
        user.value = response.data.user || response.data
        localStorage.setItem('user', JSON.stringify(user.value))
      } else {
        // Token可能已过期或无效
        console.warn('Failed to fetch user data, token may be expired')
        logout()
      }
    } catch (error: any) {
      console.error('Fetch user error:', error)

      // 根据错误类型决定是否注销
      if (error.response?.status === 401) {
        console.warn('Token expired or invalid, logging out')
        logout()
      } else {
        console.error('Network or other error, keeping user logged in')
      }
    }
  }
  
  // 刷新token
  const refreshToken = async (): Promise<boolean> => {
    try {
      if (!token.value) return false

      const response = await authApi.refresh()
      if (response.success && response.data) {
        token.value = response.data.token
        user.value = response.data.user
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))

        console.log('Token refreshed successfully')
        return true
      }

      return false
    } catch (error) {
      console.error('Token refresh failed:', error)
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value && validateToken(token.value)) {
      await fetchUser()
    } else if (token.value) {
      // Token格式无效，清除
      console.warn('Invalid token format, clearing auth state')
      logout()
    }
  }

  // 定期检查token有效性
  const startTokenValidation = () => {
    setInterval(async () => {
      if (token.value && isAuthenticated.value) {
        try {
          await fetchUser()
        } catch (error) {
          console.error('Token validation check failed:', error)
        }
      }
    }, 5 * 60 * 1000) // 每5分钟检查一次
  }

  // 更新用户信息
  const updateUserInfo = (updates: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...updates }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    fetchUser,
    refreshToken,
    validateToken,
    initAuth,
    startTokenValidation,
    updateUserInfo
  }
})
