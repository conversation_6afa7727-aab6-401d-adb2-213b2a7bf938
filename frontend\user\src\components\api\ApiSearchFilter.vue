<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import type { ApiCategory, ApiSearchParams } from '@/types'

interface Props {
  categories: ApiCategory[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  search: [params: ApiSearchParams]
  clear: []
}>()

const showFilters = ref(false)

const searchForm = reactive<ApiSearchParams>({
  keyword: '',
  category_id: undefined,
  method: '',
  is_public: undefined,
  requires_auth: undefined,
  sort_by: 'name',
  sort_order: 'asc',
  per_page: 20
})

const httpMethods = [
  { value: '', label: '全部方法' },
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
]

const sortOptions = [
  { value: 'name', label: '按名称' },
  { value: 'created_at', label: '按创建时间' },
  { value: 'rating', label: '按评分' },
  { value: 'popularity', label: '按热度' }
]

const handleSearch = () => {
  const params: ApiSearchParams = { ...searchForm }
  
  // 清理空值
  Object.keys(params).forEach(key => {
    const value = params[key as keyof ApiSearchParams]
    if (value === '' || value === undefined) {
      delete params[key as keyof ApiSearchParams]
    }
  })
  
  emit('search', params)
}

const handleClear = () => {
  Object.assign(searchForm, {
    keyword: '',
    category_id: undefined,
    method: '',
    is_public: undefined,
    requires_auth: undefined,
    sort_by: 'name',
    sort_order: 'asc'
  })
  emit('clear')
}

// 监听关键词变化，实现实时搜索
watch(() => searchForm.keyword, () => {
  handleSearch()
})

// 监听其他筛选条件变化
watch([
  () => searchForm.category_id,
  () => searchForm.method,
  () => searchForm.is_public,
  () => searchForm.requires_auth,
  () => searchForm.sort_by,
  () => searchForm.sort_order
], () => {
  handleSearch()
})
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <!-- 搜索栏 -->
    <div class="flex gap-4 mb-4">
      <div class="flex-1 relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
        </div>
        <input
          v-model="searchForm.keyword"
          type="text"
          placeholder="搜索API名称、描述或端点..."
          class="form-input pl-10 w-full"
          :disabled="loading"
        />
      </div>
      
      <button
        @click="showFilters = !showFilters"
        class="btn-outline flex items-center gap-2"
        :class="{ 'bg-primary-50 border-primary-300 text-primary-700': showFilters }"
      >
        <FunnelIcon class="w-4 h-4" />
        筛选
      </button>
      
      <button
        @click="handleClear"
        class="btn-outline text-gray-600 hover:text-gray-800"
      >
        清空
      </button>
    </div>

    <!-- 筛选器 -->
    <div v-show="showFilters" class="border-t border-gray-200 pt-4 animate-slide-down">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 分类筛选 -->
        <div>
          <label class="form-label">分类</label>
          <select v-model="searchForm.category_id" class="form-input">
            <option :value="undefined">全部分类</option>
            <option 
              v-for="category in categories" 
              :key="category.id" 
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- HTTP方法筛选 -->
        <div>
          <label class="form-label">HTTP方法</label>
          <select v-model="searchForm.method" class="form-input">
            <option 
              v-for="method in httpMethods" 
              :key="method.value" 
              :value="method.value"
            >
              {{ method.label }}
            </option>
          </select>
        </div>

        <!-- 访问权限筛选 -->
        <div>
          <label class="form-label">访问权限</label>
          <select v-model="searchForm.is_public" class="form-input">
            <option :value="undefined">全部</option>
            <option :value="true">公开API</option>
            <option :value="false">私有API</option>
          </select>
        </div>

        <!-- 认证要求筛选 -->
        <div>
          <label class="form-label">认证要求</label>
          <select v-model="searchForm.requires_auth" class="form-input">
            <option :value="undefined">全部</option>
            <option :value="true">需要认证</option>
            <option :value="false">无需认证</option>
          </select>
        </div>
      </div>

      <!-- 排序选项 -->
      <div class="mt-4 flex items-center gap-4">
        <div>
          <label class="form-label">排序方式</label>
          <select v-model="searchForm.sort_by" class="form-input">
            <option 
              v-for="option in sortOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>
        
        <div>
          <label class="form-label">排序顺序</label>
          <select v-model="searchForm.sort_order" class="form-input">
            <option value="asc">升序</option>
            <option value="desc">降序</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 组件特定样式 */
</style>
