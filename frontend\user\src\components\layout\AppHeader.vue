<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

// Props
interface Props {
  showNavigation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showNavigation: true
})

const authStore = useAuthStore()
const mobileMenuOpen = ref(false)
const userMenuOpen = ref(false)

// 公共导航（所有用户可见）
const navigation = [
  { name: '首页', href: '/', current: false },
  { name: 'API列表', href: '/apis', current: false },
  { name: '开发文档', href: '/docs', current: false },
]

// 用户下拉菜单导航
const userNavigation = computed(() => [
  { name: '控制台', href: '/dashboard', icon: UserIcon },
  { name: '个人资料', href: '/profile', icon: Cog6ToothIcon },
  { name: 'API密钥', href: '/api-keys', icon: Cog6ToothIcon },
])

const handleLogout = () => {
  authStore.logout()
  userMenuOpen.value = false
}
</script>

<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex h-16 justify-between items-center">
        <!-- Logo -->
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center space-x-2">
            <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">API</span>
            </div>
            <span class="text-xl font-bold text-gray-900">API平台</span>
          </RouterLink>
        </div>

        <!-- Desktop Navigation -->
        <nav v-if="props.showNavigation" class="hidden md:flex space-x-8">
          <RouterLink
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            class="nav-link"
            active-class="nav-link-active"
          >
            {{ item.name }}
          </RouterLink>
        </nav>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- 未登录状态 -->
          <div v-if="!authStore.isAuthenticated" class="hidden md:flex items-center space-x-4">
            <RouterLink
              to="/auth/login"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              登录
            </RouterLink>
            <RouterLink
              to="/auth/register"
              class="btn-primary"
            >
              注册
            </RouterLink>
          </div>

          <!-- 已登录状态 -->
          <div v-else class="relative">
            <button
              @click="userMenuOpen = !userMenuOpen"
              class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <img
                class="h-8 w-8 rounded-full"
                :src="authStore.user?.avatar_url || 'http://localhost/images/default-avatar.svg'"
                :alt="authStore.user?.name"
              />
              <span class="hidden md:block text-gray-700 font-medium">{{ authStore.user?.name }}</span>
            </button>

            <!-- User Dropdown -->
            <div
              v-show="userMenuOpen"
              @click.self="userMenuOpen = false"
              class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
              <RouterLink
                v-for="item in userNavigation"
                :key="item.name"
                :to="item.href"
                @click="userMenuOpen = false"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <component :is="item.icon" class="mr-3 h-4 w-4" />
                {{ item.name }}
              </RouterLink>
              <button
                @click="handleLogout"
                class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                退出登录
              </button>
            </div>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          >
            <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
            <XMarkIcon v-else class="h-6 w-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-show="mobileMenuOpen" class="md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
        <RouterLink
          v-for="item in navigation"
          :key="item.name"
          :to="item.href"
          @click="mobileMenuOpen = false"
          class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
        >
          {{ item.name }}
        </RouterLink>
        
        <!-- Mobile auth buttons -->
        <div v-if="!authStore.isAuthenticated" class="pt-4 border-t border-gray-200">
          <RouterLink
            to="/auth/login"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
          >
            登录
          </RouterLink>
          <RouterLink
            to="/auth/register"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 text-base font-medium text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-md"
          >
            注册
          </RouterLink>
        </div>

        <!-- Mobile user menu -->
        <div v-else class="pt-4 border-t border-gray-200">
          <div class="flex items-center px-3 py-2">
            <img
              class="h-8 w-8 rounded-full"
              :src="authStore.user?.avatar_url || 'http://localhost/images/default-avatar.svg'"
              :alt="authStore.user?.name"
            />
            <div class="ml-3">
              <div class="text-base font-medium text-gray-800">{{ authStore.user?.name }}</div>
              <div class="text-sm font-medium text-gray-500">{{ authStore.user?.email }}</div>
            </div>
          </div>
          <RouterLink
            v-for="item in userNavigation"
            :key="item.name"
            :to="item.href"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
          >
            {{ item.name }}
          </RouterLink>
          <button
            @click="handleLogout"
            class="block w-full text-left px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
          >
            退出登录
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
/* Header specific styles */
</style>
