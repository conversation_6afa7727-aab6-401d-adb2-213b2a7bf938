// Copyright (c) 2025 Aoki. All rights reserved.

import axios, { type AxiosInstance, type InternalAxiosRequestConfig, type AxiosResponse } from 'axios'
import { useToast } from 'vue-toastification'

// API配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  version: 'v1'
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-API-Version': API_CONFIG.version,
    'X-Requested-With': 'XMLHttpRequest'
  }
})

// 生成请求ID
function generateRequestId(): string {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加请求ID
    const requestId = generateRequestId()
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId
    }

    // 添加认证token
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }


    // 记录请求开始时间
    ;(config as any).metadata = { startTime: Date.now() }

    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      headers: config.headers,
      data: config.data
    })

    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const config = response.config as any
    const startTime = config.metadata?.startTime
    const responseTime = startTime ? Date.now() - startTime : 0

    // 防御性数据清理（已修复后端污染问题，但保留作为安全措施）
    let responseData = response.data
    if (typeof responseData === 'string') {
      // 检查是否包含污染字符
      if (responseData.includes('��<?php') || responseData.includes('<?php')) {
        console.warn('Detected corrupted response data, attempting to clean...')
        try {
          // 移除所有污染字符，只保留JSON部分
          let cleanData = responseData.replace(/^[^\{]*/, '')

          // 确保JSON是完整的，如果被截断则尝试修复
          if (!cleanData.endsWith('}')) {
            // 尝试找到最后一个完整的JSON结构
            let braceCount = 0
            let lastValidIndex = -1
            for (let i = 0; i < cleanData.length; i++) {
              if (cleanData[i] === '{') braceCount++
              if (cleanData[i] === '}') {
                braceCount--
                if (braceCount === 0) {
                  lastValidIndex = i
                }
              }
            }
            if (lastValidIndex > -1) {
              cleanData = cleanData.substring(0, lastValidIndex + 1)
            }
          }

          responseData = JSON.parse(cleanData)
          console.log('Successfully cleaned response data')
        } catch (e) {
          console.error('Failed to parse cleaned response data:', e)
          console.log('Raw data length:', responseData.length)
          console.log('First 200 chars:', responseData.substring(0, 200))
        }
      }
    }

    // 记录响应信息
    console.log(`[API Response] ${response.status} ${config.method?.toUpperCase()} ${config.url}`, {
      status: response.status,
      responseTime: responseTime + 'ms',
      headers: response.headers,
      data: responseData
    })

    const res = responseData

    // 如果响应包含success字段，直接返回
    if (typeof res === 'object' && 'success' in res) {
      return res
    }

    // 否则包装为标准格式
    return {
      success: true,
      data: res,
      message: 'Success',
      timestamp: new Date().toISOString()
    }
  },
  (error) => {
    const toast = useToast()
    const config = error.config as any
    const startTime = config?.metadata?.startTime
    const responseTime = startTime ? Date.now() - startTime : 0

    console.error(`[API Error] ${config?.method?.toUpperCase()} ${config?.url}`, {
      error: error.message,
      responseTime: responseTime + 'ms',
      status: error.response?.status,
      data: error.response?.data
    })

    // 处理网络错误
    if (!error.response) {
      toast.error('网络连接失败，请检查网络设置')
      return Promise.reject({
        success: false,
        message: '网络连接失败',
        error: 'NETWORK_ERROR',
        timestamp: new Date().toISOString()
      })
    }

    const { status, data } = error.response
    
    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token')
        window.location.href = '/auth/login'
        toast.error('登录已过期，请重新登录')
        break
      case 403:
        toast.error('没有权限访问该资源')
        break
      case 404:
        toast.error('请求的资源不存在')
        break
      case 422:
        // 表单验证错误
        if (data.errors) {
          const firstError = Object.values(data.errors)[0] as string[]
          toast.error(firstError[0] || '表单验证失败')
        } else {
          toast.error(data.message || '请求参数错误')
        }
        break
      case 429:
        toast.error('请求过于频繁，请稍后再试')
        break
      case 500:
        toast.error('服务器内部错误，请稍后重试')
        break
      default:
        toast.error(data.message || `请求失败 (${status})`)
    }
    
    return Promise.reject(error)
  }
)

// API工具函数
export const apiUtils = {

  /**
   * 设置认证令牌
   */
  setToken(token: string) {
    localStorage.setItem('token', token)
  },

  /**
   * 获取认证令牌
   */
  getToken(): string | null {
    return localStorage.getItem('token')
  },

  /**
   * 清除认证令牌
   */
  clearToken() {
    localStorage.removeItem('token')
  },

  /**
   * 清除所有认证信息
   */
  clearAuth() {
    this.clearToken()
  },

  /**
   * 检查API健康状态
   */
  async checkHealth(): Promise<any> {
    try {
      const response = await request.get('/health')
      return response
    } catch (error) {
      console.error('Health check failed:', error)
      throw error
    }
  },

  /**
   * 获取API版本信息
   */
  async getVersionInfo(): Promise<any> {
    try {
      const response = await request.get('/version')
      return response
    } catch (error) {
      console.error('Version info failed:', error)
      throw error
    }
  },

  /**
   * 格式化API错误
   */
  formatError(error: any): string {
    if (error.response?.data?.message) {
      return error.response.data.message
    }
    if (error.message) {
      return error.message
    }
    return '未知错误'
  },

  /**
   * 检查响应是否成功
   */
  isSuccess(response: any): boolean {
    return response && response.success === true
  },

  /**
   * 获取响应数据
   */
  getData(response: any): any {
    return response?.data || null
  },

  /**
   * 获取响应消息
   */
  getMessage(response: any): string {
    return response?.message || ''
  }
}

export default request
