<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import {
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  MapPinIcon,
  ClockIcon,
  XMarkIcon,
  QrCodeIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/vue/24/outline'
import { userApi } from '@/api/user'
import type { SecuritySettings } from '@/types/user'

const toast = useToast()

const loading = ref(false)
const securitySettings = ref<SecuritySettings | null>(null)
const showTwoFactorSetup = ref(false)
const twoFactorData = ref<any>(null)
const verificationCode = ref('')

// 密码修改相关
const showPasswordModal = ref(false)
const passwordLoading = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const passwordForm = reactive({
  current_password: '',
  new_password: '',
  new_password_confirmation: ''
})

// 加载安全设置
const loadSecuritySettings = async () => {
  try {
    loading.value = true
    const response = await userApi.getSecuritySettings()
    if (response.success) {
      securitySettings.value = response.data
    }
  } catch (error) {
    console.error('Failed to load security settings:', error)
    toast.error('加载安全设置失败')
  } finally {
    loading.value = false
  }
}

// 切换两步验证
const toggleTwoFactor = async (enabled: boolean) => {
  try {
    loading.value = true
    const response = await userApi.toggleTwoFactor(enabled)
    
    if (response.success) {
      if (enabled && response.data) {
        twoFactorData.value = response.data
        showTwoFactorSetup.value = true
      } else {
        toast.success(response.message)
        await loadSecuritySettings()
      }
    }
  } catch (error: any) {
    console.error('Failed to toggle two factor:', error)
    toast.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// 完成两步验证设置
const completeTwoFactorSetup = () => {
  if (!verificationCode.value) {
    toast.error('请输入验证码')
    return
  }
  
  // 这里应该验证验证码
  toast.success('两步验证设置成功')
  showTwoFactorSetup.value = false
  twoFactorData.value = null
  verificationCode.value = ''
  loadSecuritySettings()
}

// 终止会话
const terminateSession = async (sessionId: string) => {
  try {
    await userApi.terminateSession(sessionId)
    toast.success('会话已终止')
    await loadSecuritySettings()
  } catch (error: any) {
    console.error('Failed to terminate session:', error)
    toast.error(error.message || '操作失败')
  }
}

// 获取设备图标
const getDeviceIcon = (device: string) => {
  if (device.toLowerCase().includes('mobile') || device.toLowerCase().includes('iphone') || device.toLowerCase().includes('android')) {
    return DevicePhoneMobileIcon
  }
  return ComputerDesktopIcon
}

// 打开密码修改模态框
const openPasswordModal = () => {
  showPasswordModal.value = true
  // 重置表单
  passwordForm.current_password = ''
  passwordForm.new_password = ''
  passwordForm.new_password_confirmation = ''
  // 重置密码可见性
  showCurrentPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
}

// 关闭密码修改模态框
const closePasswordModal = () => {
  showPasswordModal.value = false
  passwordForm.current_password = ''
  passwordForm.new_password = ''
  passwordForm.new_password_confirmation = ''
}

// 修改密码
const changePassword = async () => {
  // 表单验证
  if (!passwordForm.current_password) {
    toast.error('请输入当前密码')
    return
  }

  if (!passwordForm.new_password) {
    toast.error('请输入新密码')
    return
  }

  if (passwordForm.new_password.length < 8) {
    toast.error('新密码长度至少8位')
    return
  }

  if (passwordForm.new_password !== passwordForm.new_password_confirmation) {
    toast.error('新密码确认不匹配')
    return
  }

  try {
    passwordLoading.value = true
    await userApi.changePassword(passwordForm)
    toast.success('密码修改成功')
    closePasswordModal()
  } catch (error: any) {
    console.error('Failed to change password:', error)
    toast.error(error.message || '密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 格式化时间
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}



// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    toast.error('复制失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSecuritySettings()
})
</script>

<template>
  <div class="space-y-8">
    <!-- 密码设置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <KeyIcon class="w-6 h-6 text-primary-600" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">密码设置</h3>
            <p class="text-sm text-gray-600">定期更改密码以保护您的账户安全</p>
          </div>
        </div>

        <button
          @click="openPasswordModal"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          <KeyIcon class="w-4 h-4 mr-2" />
          修改密码
        </button>
      </div>

      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
            <ShieldCheckIcon class="w-6 h-6 text-primary-600" />
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">密码强度</p>
            <p class="text-sm text-gray-600">建议使用包含大小写字母、数字和特殊字符的强密码</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 两步验证 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <ShieldCheckIcon class="w-6 h-6 text-primary-600" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">两步验证</h3>
            <p class="text-sm text-gray-600">为您的账户添加额外的安全保护</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <span 
            :class="[
              'px-2 py-1 text-xs font-medium rounded-full',
              securitySettings?.two_factor_enabled 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            ]"
          >
            {{ securitySettings?.two_factor_enabled ? '已启用' : '未启用' }}
          </span>
          
          <button
            @click="toggleTwoFactor(!securitySettings?.two_factor_enabled)"
            :disabled="loading"
            :class="[
              'px-4 py-2 rounded-md font-medium transition-colors disabled:opacity-50',
              securitySettings?.two_factor_enabled
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-primary-600 hover:bg-primary-700 text-white'
            ]"
          >
            {{ loading ? '处理中...' : (securitySettings?.two_factor_enabled ? '禁用' : '启用') }}
          </button>
        </div>
      </div>
      
      <div class="text-sm text-gray-600">
        <p>两步验证可以防止他人未经授权访问您的账户，即使他们知道您的密码。</p>
      </div>
    </div>



    <!-- 活跃会话 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center space-x-3 mb-6">
        <ComputerDesktopIcon class="w-6 h-6 text-primary-600" />
        <div>
          <h3 class="text-lg font-semibold text-gray-900">活跃会话</h3>
          <p class="text-sm text-gray-600">管理您在不同设备上的登录会话</p>
        </div>
      </div>
      
      <div v-if="securitySettings?.active_sessions" class="space-y-4">
        <div
          v-for="session in securitySettings.active_sessions"
          :key="session.id"
          class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
        >
          <div class="flex items-center space-x-4">
            <div class="p-2 bg-gray-100 rounded-lg">
              <component :is="getDeviceIcon(session.device)" class="w-6 h-6 text-gray-600" />
            </div>
            
            <div>
              <div class="flex items-center space-x-2">
                <h4 class="font-medium text-gray-900">{{ session.device }}</h4>
                <span v-if="session.is_current" class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  当前会话
                </span>
              </div>
              <div class="text-sm text-gray-600 space-y-1">
                <div class="flex items-center space-x-4">
                  <span>{{ session.browser }}</span>
                  <span class="flex items-center">
                    <MapPinIcon class="w-4 h-4 mr-1" />
                    {{ session.location || '未知位置' }}
                  </span>
                </div>
                <div class="flex items-center text-gray-500">
                  <ClockIcon class="w-4 h-4 mr-1" />
                  最后活跃: {{ formatDate(session.last_activity) }}
                </div>
                <div class="text-xs text-gray-400">
                  IP: {{ session.ip_address }}
                </div>
              </div>
            </div>
          </div>
          
          <button
            v-if="!session.is_current"
            @click="terminateSession(session.id)"
            class="btn-outline btn-sm text-red-600 hover:text-red-700 hover:border-red-300"
          >
            <XMarkIcon class="w-4 h-4 mr-1" />
            终止
          </button>
        </div>
      </div>
    </div>

    <!-- 登录历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center space-x-3 mb-6">
        <ClockIcon class="w-6 h-6 text-primary-600" />
        <div>
          <h3 class="text-lg font-semibold text-gray-900">登录历史</h3>
          <p class="text-sm text-gray-600">查看最近的登录记录</p>
        </div>
      </div>
      
      <div v-if="securitySettings?.login_history" class="space-y-3">
        <div
          v-for="login in securitySettings.login_history.slice(0, 10)"
          :key="login.id"
          class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                login.status === 'success' ? 'bg-green-500' : 'bg-red-500'
              ]"
            ></div>
            
            <div>
              <div class="text-sm font-medium text-gray-900">
                {{ login.status === 'success' ? '登录成功' : '登录失败' }}
              </div>
              <div class="text-xs text-gray-500 space-x-4">
                <span>{{ login.location || '未知位置' }}</span>
                <span>{{ login.ip_address }}</span>
              </div>
            </div>
          </div>
          
          <div class="text-xs text-gray-500">
            {{ formatDate(login.created_at) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 两步验证设置模态框 -->
    <div
      v-if="showTwoFactorSetup && twoFactorData"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click.self="showTwoFactorSetup = false"
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">设置两步验证</h3>
        </div>
        
        <div class="p-6 space-y-6">
          <!-- 二维码 -->
          <div class="text-center">
            <div class="w-48 h-48 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <QrCodeIcon class="w-24 h-24 text-gray-400" />
            </div>
            <p class="text-sm text-gray-600">使用身份验证器应用扫描二维码</p>
          </div>
          
          <!-- 密钥 -->
          <div>
            <label class="form-label">手动输入密钥</label>
            <div class="flex items-center space-x-2">
              <code class="flex-1 text-sm bg-gray-100 px-3 py-2 rounded font-mono">
                {{ twoFactorData.secret }}
              </code>
              <button
                @click="copyToClipboard(twoFactorData.secret)"
                class="btn-outline btn-sm"
              >
                复制
              </button>
            </div>
          </div>
          
          <!-- 验证码输入 -->
          <div>
            <label class="form-label">验证码</label>
            <input
              v-model="verificationCode"
              type="text"
              class="form-input"
              placeholder="请输入6位验证码"
              maxlength="6"
            />
          </div>
          
          <!-- 备用代码 -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">备用恢复代码</h4>
            <p class="text-sm text-yellow-700 mb-3">请保存这些代码，在无法使用身份验证器时可以用来登录：</p>
            <div class="grid grid-cols-2 gap-2 text-sm font-mono">
              <code
                v-for="code in twoFactorData.backup_codes"
                :key="code"
                class="bg-white px-2 py-1 rounded border"
              >
                {{ code }}
              </code>
            </div>
          </div>
          
          <div class="flex justify-end space-x-4">
            <button
              @click="showTwoFactorSetup = false"
              class="btn-outline"
            >
              取消
            </button>
            <button
              @click="completeTwoFactorSetup"
              class="btn-primary"
            >
              完成设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 密码修改模态框 -->
    <div
      v-if="showPasswordModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[9999] backdrop-blur-sm"
      @click.self="closePasswordModal"
      style="position: fixed; top: 0; left: 0; right: 0; bottom: 0;"
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">修改密码</h3>
            <button
              @click="closePasswordModal"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon class="w-6 h-6" />
            </button>
          </div>
        </div>

        <form @submit.prevent="changePassword" class="p-6 space-y-6">
          <!-- 当前密码 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              当前密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <input
                v-model="passwordForm.current_password"
                :type="showCurrentPassword ? 'text' : 'password'"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 pr-10"
                placeholder="请输入当前密码"
              />
              <button
                type="button"
                @click="showCurrentPassword = !showCurrentPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="!showCurrentPassword" class="w-5 h-5" />
                <EyeSlashIcon v-else class="w-5 h-5" />
              </button>
            </div>
          </div>

          <!-- 新密码 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              新密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <input
                v-model="passwordForm.new_password"
                :type="showNewPassword ? 'text' : 'password'"
                required
                minlength="8"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 pr-10"
                placeholder="请输入新密码（至少8位）"
              />
              <button
                type="button"
                @click="showNewPassword = !showNewPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="!showNewPassword" class="w-5 h-5" />
                <EyeSlashIcon v-else class="w-5 h-5" />
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">
              密码至少8位，建议包含大小写字母、数字和特殊字符
            </p>
          </div>

          <!-- 确认新密码 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              确认新密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <input
                v-model="passwordForm.new_password_confirmation"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 pr-10"
                placeholder="请再次输入新密码"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="!showConfirmPassword" class="w-5 h-5" />
                <EyeSlashIcon v-else class="w-5 h-5" />
              </button>
            </div>
          </div>

          <!-- 安全提示 -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <ShieldCheckIcon class="w-5 h-5 text-blue-600 mt-0.5" />
              <div class="text-sm text-blue-800">
                <p class="font-medium mb-1">安全提示</p>
                <ul class="space-y-1 text-xs">
                  <li>• 修改密码后，您需要在所有设备上重新登录</li>
                  <li>• 请确保新密码足够复杂且不易被猜测</li>
                  <li>• 不要与他人分享您的密码</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="flex justify-end space-x-4">
            <button
              type="button"
              @click="closePasswordModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="passwordLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ passwordLoading ? '修改中...' : '修改密码' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-sm {
  @apply px-2 py-1 text-xs;
}
</style>
