<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { useSystemStore } from '@/stores/system'
import {
  CloudIcon,
  LockClosedIcon,
  ServerIcon,
  ChartBarIcon,
  CogIcon,
  ShieldCheckIcon,
  ArrowRightIcon
} from '@heroicons/vue/24/outline'
import { apisApi } from '@/api/apis'
import ApiCard from '@/components/api/ApiCard.vue'
import type { ApiEndpoint, ApiStats } from '@/types'

const features = [
  {
    name: '高性能API',
    description: '基于现代架构设计，提供高性能、低延迟的API服务',
    icon: ServerIcon,
  },
  {
    name: '安全可靠',
    description: '企业级安全保障，支持多种认证方式和权限控制',
    icon: LockClosedIcon,
  },
  {
    name: '云原生',
    description: '支持容器化部署，弹性扩缩容，适应各种业务场景',
    icon: CloudIcon,
  },
  {
    name: '实时监控',
    description: '全方位监控API性能，提供详细的使用统计和分析',
    icon: ChartBarIcon,
  },
  {
    name: '易于集成',
    description: '提供丰富的SDK和文档，快速集成到您的应用中',
    icon: CogIcon,
  },
  {
    name: '数据保护',
    description: '严格的数据保护措施，确保您的数据安全和隐私',
    icon: ShieldCheckIcon,
  },
]

// 响应式数据
const systemStore = useSystemStore()
const loading = ref(false)
const popularApis = ref<ApiEndpoint[]>([])
const apiStats = ref<ApiStats | null>(null)

const stats = [
  { name: 'API调用次数', value: '1.2M+' },
  { name: '注册开发者', value: '10K+' },
  { name: '可用API', value: '200+' },
  { name: '服务可用性', value: '99.9%' },
]





// 加载热门API
const loadPopularApis = async () => {
  try {
    const response = await apisApi.getPopularApis(6)
    if (response.success && response.data && Array.isArray(response.data)) {
      // 按照total_calls降序排列，取前3名
      const sortedApis = response.data
        .filter(api => api.total_calls && api.total_calls > 0) // 只显示有调用次数的API
        .sort((a, b) => (b.total_calls || 0) - (a.total_calls || 0))
        .slice(0, 3)

      popularApis.value = sortedApis
    } else {
      popularApis.value = []
    }
  } catch (error) {
    console.error('Failed to load popular APIs:', error)
    popularApis.value = []
  }
}
// 加载API统计
const loadApiStats = async () => {
  try {
    const response = await apisApi.getApiStats()
    if (response.success && response.data) {
      apiStats.value = response.data
      // 更新统计数据
      const totalApis = apiStats.value.total_apis || 0
      stats[0].value = `${(totalApis * 1000).toLocaleString()}+`
      stats[1].value = `${Math.floor(totalApis / 20)}K+`
      stats[2].value = `${totalApis}+`
    } else {
      // 使用默认值
      stats[0].value = '1.2M+'
      stats[1].value = '10K+'
      stats[2].value = '200+'
    }
  } catch (error) {
    console.error('Failed to load API stats:', error)
    // 使用默认值
    stats[0].value = '1.2M+'
    stats[1].value = '10K+'
    stats[2].value = '200+'
  }
}

// 加载可信数据（优先后台手动配置）
const loadTrustedData = async () => {
  try {
    // 如果公开设置已携带 homepage.trustedData，优先使用
    const cached = systemStore.settings.homepage?.trustedData
    if (cached) {
      stats[0].value = `${(cached.api_calls).toLocaleString()}+`
      stats[1].value = `${(cached.developers / 1000).toFixed(0)}K+`
      stats[2].value = `${cached.apis}+`
      stats[3].value = `${cached.availability}%`
      return
    }

    // 兜底：请求 /api/trusted-data
    const res = await fetch('/api/trusted-data').then(r => r.json())
    if (res?.success && res.data) {
      const d = res.data
      stats[0].value = `${(d.api_calls).toLocaleString()}+`
      stats[1].value = `${(d.developers / 1000).toFixed(0)}K+`
      stats[2].value = `${d.apis}+`
      stats[3].value = `${d.availability}%`
    }
  } catch (e) {
    // 保持默认展示
  }
}


// 组件挂载时加载数据
onMounted(async () => {
  await systemStore.initSettings()
  loadPopularApis()
  loadApiStats()
  loadTrustedData()
})
</script>

<template>
  <div class="bg-white">
    <!-- Hero Section -->
    <div class="relative isolate px-6 pt-14 lg:px-8">
      <div class="absolute inset-x-0 -top-40 -z-50 transform-gpu overflow-hidden blur-3xl sm:-top-80 pointer-events-none">
        <div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"></div>
      </div>

      <div class="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
        <div class="text-center">
          <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
            {{ systemStore.systemName }}
          </h1>
          <p class="mt-6 text-lg leading-8 text-gray-600">
            {{ systemStore.systemDescription }}
          </p>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <RouterLink
              to="/auth/register"
              class="btn-primary text-lg px-8 py-3"
            >
              开始使用
            </RouterLink>
            <RouterLink
              to="/apis"
              class="text-lg font-semibold leading-6 text-gray-900 hover:text-primary-600"
            >
              浏览API <span aria-hidden="true">→</span>
            </RouterLink>
          </div>
        </div>
      </div>

      <div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-50 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)] pointer-events-none">
        <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:text-center">
          <h2 class="text-base font-semibold leading-7 text-primary-600">强大功能</h2>
          <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            为开发者而生的API平台
          </p>
          <p class="mt-6 text-lg leading-8 text-gray-600">
            我们提供完整的API生态系统，从开发到部署，从监控到分析，一站式解决您的所有需求。
          </p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            <div v-for="feature in features" :key="feature.name" class="relative pl-16">
              <dt class="text-base font-semibold leading-7 text-gray-900">
                <div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600">
                  <component :is="feature.icon" class="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                {{ feature.name }}
              </dt>
              <dd class="mt-2 text-base leading-7 text-gray-600">{{ feature.description }}</dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="bg-gray-50 py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:max-w-none">
          <div class="text-center">
            <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              值得信赖的数据
            </h2>
            <p class="mt-4 text-lg leading-8 text-gray-600">
              数以万计的开发者选择我们的平台构建他们的应用
            </p>
          </div>
          <dl class="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
            <div v-for="stat in stats" :key="stat.name" class="flex flex-col bg-white p-8">
              <dt class="text-sm font-semibold leading-6 text-gray-600">{{ stat.name }}</dt>
              <dd class="order-first text-3xl font-semibold tracking-tight text-gray-900">{{ stat.value }}</dd>
            </div>
          </dl>
        </div>
      </div>
    </div>





    <!-- 热门API Section -->
    <div v-if="popularApis.length >= 3" class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
          <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            热门API
          </h2>
          <p class="mt-4 text-lg leading-8 text-gray-600">
            探索最受欢迎的API服务，快速集成到您的应用中
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <ApiCard
            v-for="api in popularApis"
            :key="api.id"
            :api="api"
            :show-category="true"
          />
        </div>

        <div class="text-center">
          <RouterLink
            to="/apis?sort_by=calls&sort_order=desc"
            class="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium"
          >
            查看更多热门API
            <ArrowRightIcon class="w-4 h-4" />
          </RouterLink>
        </div>
      </div>
    </div>
    <!-- CTA Section -->
    <div class="bg-primary-600">
      <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            准备开始了吗？
          </h2>
          <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100">
            立即注册，获取API密钥，开始构建您的下一个伟大应用。
          </p>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <RouterLink
              to="/auth/register"
              class="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-primary-600 shadow-sm hover:bg-primary-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
            >
              免费注册
            </RouterLink>
            <RouterLink
              to="/docs"
              class="text-sm font-semibold leading-6 text-white hover:text-primary-100"
            >
              查看文档 <span aria-hidden="true">→</span>
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Home page specific styles */
</style>
