// Copyright (c) 2025 Aoki. All rights reserved.

import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { systemApi, type SystemSettings } from '@/api/system'

export const useSystemStore = defineStore('system', () => {
  // 系统设置
  const settings = reactive<SystemSettings>({
    basic: {
      systemName: 'API平台',
      systemDescription: '现代化API平台用户端应用',
      systemVersion: '1.0.0',
      adminEmail: '<EMAIL>',
      timezone: 'Asia/Shanghai',
      defaultLanguage: 'zh-CN'
    },
    api: {
      baseUrl: 'http://localhost/api',
      version: 'v1',
      timeout: 30000,
      maxConcurrency: 100,
      enableCache: true,
      cacheExpiration: 3600
    },
    homepage: {
      trustedData: {
        api_calls: 0,
        developers: 0,
        apis: 0,
        availability: 99.9
      }

    }
  })

  // 加载状态
  const loading = ref(false)
  const lastUpdated = ref<Date | null>(null)

  // 计算属性
  const systemName = computed(() => settings.basic.systemName)
  const systemDescription = computed(() => settings.basic.systemDescription)

  /**
   * 加载系统设置
   */
  const loadSettings = async (): Promise<boolean> => {
    try {
      loading.value = true
      const response = await systemApi.getPublicSettings()

      if (response.success && response.data) {
        Object.assign(settings, response.data)
        lastUpdated.value = new Date()

        // 缓存到本地存储
        localStorage.setItem('systemSettings', JSON.stringify(settings))
        localStorage.setItem('systemSettingsUpdated', new Date().toISOString())

        console.log('系统设置加载成功')
        return true
      } else {
        console.warn('获取系统设置失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('加载系统设置失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 从缓存恢复设置
   */
  const restoreFromCache = (): boolean => {
    try {
      const cachedSettings = localStorage.getItem('systemSettings')
      const cachedTime = localStorage.getItem('systemSettingsUpdated')

      if (cachedSettings && cachedTime) {
        const parsedSettings = JSON.parse(cachedSettings)
        const updatedTime = new Date(cachedTime)

        // 检查缓存是否过期（1小时）
        const now = new Date()
        const timeDiff = now.getTime() - updatedTime.getTime()
        const oneHour = 60 * 60 * 1000

        if (timeDiff < oneHour) {
          Object.assign(settings, parsedSettings)
          lastUpdated.value = updatedTime
          console.log('从缓存恢复系统设置')
          return true
        }
      }
      return false
    } catch (error) {
      console.error('恢复缓存设置失败:', error)
      return false
    }
  }

  /**
   * 初始化系统设置
   */
  const initSettings = async (): Promise<void> => {
    console.log('初始化系统设置...')

    // 先尝试从缓存恢复
    const cacheRestored = restoreFromCache()

    // 尝试从服务器加载最新设置
    const serverLoaded = await loadSettings()

    // 如果服务器加载失败但有缓存，继续使用缓存
    if (!serverLoaded && !cacheRestored) {
      console.warn('无法加载系统设置，使用默认配置')
    }

    console.log('系统设置初始化完成')
  }

  return {
    settings,
    loading,
    lastUpdated,
    systemName,
    systemDescription,
    loadSettings,
    restoreFromCache,
    initSettings
  }
})
