// Copyright (c) 2025 Aoki. All rights reserved.

// Mock API数据
export const mockApis = [
  {
    id: 1,
    category_id: 1,
    name: '用户认证API',
    description: '提供用户登录、注册、密码重置等认证功能',
    method: 'POST',
    endpoint: '/api/auth/login',
    status: 'active',
    version: '1.0.0',
    lastUpdated: '2025-08-10',
    is_public: true,
    is_active: true,
    requires_auth: false,
    total_calls: 1250,
    created_at: '2025-08-10T10:00:00Z',
    updated_at: '2025-08-10T10:00:00Z',
    category: { name: '认证' }
  },
  {
    id: 2,
    category_id: 2,
    name: '用户信息API',
    description: '获取和更新用户个人信息',
    method: 'GET',
    endpoint: '/api/user/profile',
    status: 'active',
    version: '1.0.0',
    lastUpdated: '2025-08-10',
    is_public: false,
    is_active: true,
    requires_auth: true,
    total_calls: 890,
    created_at: '2025-08-10T11:00:00Z',
    updated_at: '2025-08-10T11:00:00Z',
    category: { name: '用户管理' }
  },
  {
    id: 3,
    category_id: 3,
    name: '文件上传API',
    description: '支持多种格式文件上传',
    method: 'POST',
    endpoint: '/api/upload',
    status: 'active',
    version: '1.0.0',
    lastUpdated: '2025-08-10',
    is_public: false,
    is_active: true,
    requires_auth: true,
    total_calls: 456,
    created_at: '2025-08-10T12:00:00Z',
    updated_at: '2025-08-10T12:00:00Z',
    category: { name: '文件管理' }
  }
]

// Mock统计数据
export const mockStats = {
  total_apis: 156,
  public_apis: 89,
  categories_count: 12,
  average_rating: 4.2,
  total_favorites: 234,
  totalApis: 156,
  activeApis: 142,
  totalRequests: 1234567,
  avgResponseTime: 245
}

// Mock收藏数据
export const mockFavorites = [
  {
    id: 1,
    user_id: 1,
    api_id: 1,
    created_at: '2025-08-10T10:00:00Z',
    updated_at: '2025-08-10T10:00:00Z'
  }
]

// Mock评分数据
export const mockRatings = [
  {
    id: 1,
    user_id: 1,
    api_id: 1,
    rating: 5,
    comment: '非常好用的API',
    created_at: '2025-08-10T10:00:00Z',
    updated_at: '2025-08-10T10:00:00Z',
    user: {
      id: 1,
      name: '张三',
      avatar: 'https://ui-avatars.com/api/?name=张三&background=3b82f6&color=fff'
    }
  }
]
