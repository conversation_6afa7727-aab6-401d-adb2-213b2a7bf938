// Copyright (c) 2025 Aoki. All rights reserved.

// 用户个人信息
export interface UserProfile {
  id: number
  name: string
  email: string
  avatar?: string
  avatar_url?: string
  phone?: string
  company?: string
  website?: string
  bio?: string
  location?: string
  timezone?: string
  email_verified_at?: string
  phone_verified_at?: string
  created_at: string
  updated_at: string
}

// 用户统计信息
export interface UserStats {
  total_api_calls: number
  total_api_keys: number
  total_favorites: number
  monthly_api_calls: number
  daily_api_calls: number
  success_rate: number
  average_response_time: number
  most_used_api?: {
    id: number
    name: string
    calls: number
  }
}

// API使用统计
export interface ApiUsageStats {
  date: string
  api_calls: number
  success_calls: number
  failed_calls: number
  average_response_time: number
}

// 账单信息
export interface Bill {
  id: number
  user_id: number
  amount: number
  currency: string
  status: 'pending' | 'paid' | 'failed' | 'cancelled'
  billing_period_start: string
  billing_period_end: string
  items: BillItem[]
  created_at: string
  updated_at: string
}

export interface BillItem {
  id: number
  description: string
  quantity: number
  unit_price: number
  total_price: number
}

// 套餐信息
export interface Plan {
  id: number
  name: string
  description: string
  price: number
  currency: string
  billing_cycle: 'monthly' | 'yearly'
  features: PlanFeature[]
  limits: PlanLimits
  is_popular?: boolean
  is_current?: boolean
}

export interface PlanFeature {
  name: string
  description: string
  included: boolean
}

export interface PlanLimits {
  api_calls_per_month: number
  api_keys_limit: number
  rate_limit_per_minute: number
  support_level: 'basic' | 'standard' | 'premium'
}

// 安全设置
export interface SecuritySettings {
  two_factor_enabled: boolean
  login_notifications: boolean
  api_key_notifications: boolean
  password_last_changed: string
  active_sessions: SecuritySession[]
  login_history: LoginHistory[]
}

export interface SecuritySession {
  id: string
  device: string
  browser: string
  ip_address: string
  location?: string
  last_activity: string
  is_current: boolean
}

export interface LoginHistory {
  id: number
  ip_address: string
  user_agent: string
  location?: string
  status: 'success' | 'failed'
  created_at: string
}

// 通知消息
export interface Notification {
  id: number
  user_id: number
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  data?: any
  read_at?: string
  created_at: string
}

// 通知设置
export interface NotificationSettings {
  email_notifications: boolean
  push_notifications: boolean
  api_status_notifications: boolean
  billing_notifications: boolean
  security_notifications: boolean
  marketing_notifications: boolean
}

// 表单数据类型
export interface UpdateProfileData {
  name: string
  phone?: string
  company?: string
  website?: string
  bio?: string
  location?: string
  timezone?: string
}

export interface ChangePasswordData {
  current_password: string
  new_password: string
  new_password_confirmation: string
}

export interface TwoFactorSetupData {
  secret: string
  qr_code: string
  backup_codes: string[]
}

// API响应类型
export interface UserProfileResponse {
  success: boolean
  data: UserProfile
  message: string
}

export interface UserStatsResponse {
  success: boolean
  data: UserStats
  message: string
}

export interface BillsResponse {
  success: boolean
  data: Bill[]
  message: string
}

export interface PlansResponse {
  success: boolean
  data: Plan[]
  message: string
}

export interface NotificationsResponse {
  success: boolean
  data: Notification[]
  message: string
}
