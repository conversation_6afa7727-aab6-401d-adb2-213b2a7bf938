// Copyright (c) 2025 Aoki. All rights reserved.

import request from '@/utils/request'
import type { LoginCredentials, RegisterData, AuthResponse, UserResponse } from '@/types/auth'

// 使用真实API
const isDev = false

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const authApi = {
  // 登录
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    if (isDev) {
      await delay(800)
      // 模拟登录验证
      if ((credentials.email === '<EMAIL>' && credentials.password === 'user123456') ||
          (credentials.email === '<EMAIL>' && credentials.password === 'developer123456')) {
        return {
          success: true,
          data: {
            user: {
              id: credentials.email === '<EMAIL>' ? 4 : 3,
              name: credentials.email === '<EMAIL>' ? 'API 用户' : 'API 开发者',
              email: credentials.email,
              avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(credentials.email === '<EMAIL>' ? 'API 用户' : 'API 开发者')}&background=3b82f6&color=fff`,
              email_verified_at: '2025-08-10T17:59:10.000000Z',
              created_at: '2025-08-10T17:59:10.000000Z',
              updated_at: '2025-08-12T08:40:00.000000Z'
            },

            // mock增加过期时间字段，满足类型
            expires_at: new Date(Date.now() + 3600_000).toISOString(),
            token: 'mock_jwt_token_' + Date.now()
          },
          message: '登录成功'
        }
      } else {
        return Promise.reject(new Error('邮箱或密码错误'))
      }
    }
    return request.post('/auth/login', credentials)
  },

  // 注册
  register: async (data: RegisterData): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(1000)
      return {
        success: true,
        message: '注册成功，请登录'
      }
    }
    return request.post('/auth/register', data)
  },

  // 注销
  logout: async (): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(300)
      return {
        success: true,
        message: '注销成功'
      }
    }
    return request.post('/auth/logout')
  },

  // 获取当前用户信息
  me: async (): Promise<UserResponse> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        data: {
          user: {
            id: 1,
            name: '张三',
            email: '<EMAIL>',
            avatar: 'https://ui-avatars.com/api/?name=张三&background=3b82f6&color=fff',
            email_verified_at: '2024-01-01T00:00:00Z',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-15T00:00:00Z'
          }
        },
        message: 'Success'
      }
    }
    return request.get('/auth/me')
  },

  // 刷新token
  refresh: (): Promise<AuthResponse> => {
    return request.post('/auth/refresh')
  },

  // 忘记密码
  forgotPassword: async (email: string): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(1000)
      return {
        success: true,
        message: '密码重置邮件已发送'
      }
    }
    return request.post('/auth/forgot-password', { email })
  },

  // 重置密码
  resetPassword: async (data: { token: string; email: string; password: string; password_confirmation: string }): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(1000)
      return {
        success: true,
        message: '密码重置成功'
      }
    }
    return request.post('/auth/reset-password', data)
  }
}
