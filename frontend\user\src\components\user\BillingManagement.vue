<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useToast } from 'vue-toastification'
import { 
  CreditCardIcon,
  DocumentTextIcon,
  CheckIcon,
  StarIcon,
  ArrowUpIcon
} from '@heroicons/vue/24/outline'
import { userApi } from '@/api/user'
import type { Bill, Plan } from '@/types/user'

const toast = useToast()

const loading = ref(false)
const bills = ref<Bill[]>([])
const plans = ref<Plan[]>([])
const showUpgradeModal = ref(false)
const selectedPlan = ref<Plan | null>(null)

// 计算属性
const currentPlan = computed(() => {
  return plans.value.find(plan => plan.is_current)
})

const availablePlans = computed(() => {
  return plans.value.filter(plan => !plan.is_current)
})

// 获取账单状态样式
const getBillStatusClass = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取账单状态文本
const getBillStatusText = (status: string) => {
  switch (status) {
    case 'paid':
      return '已支付'
    case 'pending':
      return '待支付'
    case 'failed':
      return '支付失败'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

// 加载账单列表
const loadBills = async () => {
  try {
    loading.value = true
    const response = await userApi.getBills()
    if (response.success) {
      bills.value = response.data
    }
  } catch (error) {
    console.error('Failed to load bills:', error)
    toast.error('加载账单失败')
  } finally {
    loading.value = false
  }
}

// 加载套餐列表
const loadPlans = async () => {
  try {
    const response = await userApi.getPlans()
    if (response.success) {
      plans.value = response.data
    }
  } catch (error) {
    console.error('Failed to load plans:', error)
    toast.error('加载套餐失败')
  }
}

// 升级套餐
const upgradePlan = async () => {
  if (!selectedPlan.value) return
  
  try {
    loading.value = true
    await userApi.upgradePlan(selectedPlan.value.id)
    toast.success('套餐升级成功')
    showUpgradeModal.value = false
    selectedPlan.value = null
    await loadPlans()
  } catch (error: any) {
    console.error('Failed to upgrade plan:', error)
    toast.error(error.message || '套餐升级失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 格式化金额
const formatAmount = (amount: number, currency: string) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency === 'CNY' ? 'CNY' : 'USD'
  }).format(amount)
}

// 组件挂载时加载数据
onMounted(() => {
  loadBills()
  loadPlans()
})
</script>

<template>
  <div class="space-y-8">
    <!-- 当前套餐 -->
    <div v-if="currentPlan" class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center space-x-2 mb-2">
            <h3 class="text-xl font-bold">{{ currentPlan.name }}</h3>
            <StarIcon v-if="currentPlan.is_popular" class="w-5 h-5 text-yellow-300" />
          </div>
          <p class="text-primary-100 mb-4">{{ currentPlan.description }}</p>
          <div class="text-2xl font-bold">
            {{ formatAmount(currentPlan.price, currentPlan.currency) }}
            <span class="text-sm font-normal text-primary-200">
              /{{ currentPlan.billing_cycle === 'monthly' ? '月' : '年' }}
            </span>
          </div>
        </div>
        
        <div class="text-right">
          <button
            v-if="availablePlans.length > 0"
            @click="showUpgradeModal = true"
            class="bg-white text-primary-600 px-4 py-2 rounded-md font-medium hover:bg-primary-50 transition-colors"
          >
            <ArrowUpIcon class="w-4 h-4 inline mr-1" />
            升级套餐
          </button>
        </div>
      </div>
      
      <!-- 套餐限制 -->
      <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold">
            {{ currentPlan.limits.api_calls_per_month === -1 ? '无限' : currentPlan.limits.api_calls_per_month.toLocaleString() }}
          </div>
          <div class="text-sm text-primary-200">月调用量</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold">
            {{ currentPlan.limits.api_keys_limit === -1 ? '无限' : currentPlan.limits.api_keys_limit }}
          </div>
          <div class="text-sm text-primary-200">API密钥</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold">{{ currentPlan.limits.rate_limit_per_minute }}</div>
          <div class="text-sm text-primary-200">每分钟限制</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold capitalize">{{ currentPlan.limits.support_level }}</div>
          <div class="text-sm text-primary-200">支持级别</div>
        </div>
      </div>
    </div>

    <!-- 账单历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <DocumentTextIcon class="w-5 h-5 mr-2" />
          账单历史
        </h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"></div>
        <p class="text-gray-600">加载中...</p>
      </div>
      
      <div v-else-if="bills.length === 0" class="p-8 text-center">
        <DocumentTextIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h4 class="text-lg font-medium text-gray-900 mb-2">暂无账单</h4>
        <p class="text-gray-600">您的账单记录将显示在这里</p>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="bill in bills"
          :key="bill.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h4 class="text-lg font-medium text-gray-900">
                  账单 #{{ bill.id }}
                </h4>
                <span 
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    getBillStatusClass(bill.status)
                  ]"
                >
                  {{ getBillStatusText(bill.status) }}
                </span>
              </div>
              
              <div class="text-sm text-gray-600 space-y-1">
                <p>账单周期: {{ formatDate(bill.billing_period_start) }} - {{ formatDate(bill.billing_period_end) }}</p>
                <p>创建时间: {{ formatDate(bill.created_at) }}</p>
              </div>
              
              <!-- 账单项目 -->
              <div class="mt-3">
                <div
                  v-for="item in bill.items"
                  :key="item.id"
                  class="flex justify-between text-sm"
                >
                  <span class="text-gray-600">{{ item.description }} × {{ item.quantity }}</span>
                  <span class="font-medium">{{ formatAmount(item.total_price, bill.currency) }}</span>
                </div>
              </div>
            </div>
            
            <div class="text-right ml-6">
              <div class="text-2xl font-bold text-gray-900">
                {{ formatAmount(bill.amount, bill.currency) }}
              </div>
              <button
                v-if="bill.status === 'pending'"
                class="mt-2 btn-primary btn-sm"
              >
                立即支付
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 升级套餐模态框 -->
    <div
      v-if="showUpgradeModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click.self="showUpgradeModal = false"
    >
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">选择套餐</h3>
        </div>
        
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="plan in availablePlans"
              :key="plan.id"
              @click="selectedPlan = plan"
              :class="[
                'relative p-6 rounded-lg border-2 cursor-pointer transition-all',
                selectedPlan?.id === plan.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300',
                plan.is_popular ? 'ring-2 ring-primary-500' : ''
              ]"
            >
              <div v-if="plan.is_popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span class="bg-primary-500 text-white px-3 py-1 text-xs font-medium rounded-full">
                  推荐
                </span>
              </div>
              
              <div class="text-center">
                <h4 class="text-xl font-bold text-gray-900 mb-2">{{ plan.name }}</h4>
                <p class="text-gray-600 mb-4">{{ plan.description }}</p>
                <div class="text-3xl font-bold text-gray-900 mb-1">
                  {{ formatAmount(plan.price, plan.currency) }}
                </div>
                <div class="text-sm text-gray-500 mb-6">
                  /{{ plan.billing_cycle === 'monthly' ? '月' : '年' }}
                </div>
              </div>
              
              <!-- 功能列表 -->
              <ul class="space-y-2 mb-6">
                <li
                  v-for="feature in plan.features"
                  :key="feature.name"
                  class="flex items-center text-sm"
                >
                  <CheckIcon 
                    :class="[
                      'w-4 h-4 mr-2',
                      feature.included ? 'text-green-500' : 'text-gray-400'
                    ]"
                  />
                  <span :class="feature.included ? 'text-gray-900' : 'text-gray-500'">
                    {{ feature.description }}
                  </span>
                </li>
              </ul>
              
              <!-- 限制信息 -->
              <div class="text-xs text-gray-500 space-y-1">
                <div>月调用量: {{ plan.limits.api_calls_per_month === -1 ? '无限' : plan.limits.api_calls_per_month.toLocaleString() }}</div>
                <div>API密钥: {{ plan.limits.api_keys_limit === -1 ? '无限' : plan.limits.api_keys_limit }}</div>
                <div>每分钟限制: {{ plan.limits.rate_limit_per_minute }}</div>
              </div>
            </div>
          </div>
          
          <div class="flex justify-end space-x-4 mt-8">
            <button
              @click="showUpgradeModal = false"
              class="btn-outline"
            >
              取消
            </button>
            <button
              @click="upgradePlan"
              :disabled="!selectedPlan || loading"
              class="btn-primary disabled:opacity-50"
            >
              {{ loading ? '升级中...' : '确认升级' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-sm {
  @apply px-2 py-1 text-xs;
}
</style>
