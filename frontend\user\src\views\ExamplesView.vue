<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'
import {
  CodeBracketIcon,
  DocumentDuplicateIcon,
  PlayIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  UserIcon,
  ShieldCheckIcon,
  DocumentIcon,
  CreditCardIcon,
  BellIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

const toast = useToast()

// 状态管理
const activeCategory = ref('auth')
const activeLanguage = ref('javascript')
type ExampleId = string | null

const expandedExample = ref<ExampleId>(null)
const copiedCode = ref('')

// 编程语言选项
const languages = [
  { id: 'javascript', name: 'JavaScript', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'python', name: 'Python', color: 'bg-blue-100 text-blue-800' },
  { id: 'php', name: 'PHP', color: 'bg-purple-100 text-purple-800' },
  { id: 'java', name: 'Java', color: 'bg-red-100 text-red-800' },
  { id: 'curl', name: 'cURL', color: 'bg-gray-100 text-gray-800' }
]

// API分类
const categories = [
  { id: 'auth', name: '用户认证', icon: ShieldCheckIcon, color: 'text-green-600' },
  { id: 'users', name: '用户管理', icon: UserIcon, color: 'text-blue-600' },
  { id: 'files', name: '文件处理', icon: DocumentIcon, color: 'text-purple-600' },
  { id: 'payments', name: '支付接口', icon: CreditCardIcon, color: 'text-orange-600' },
  { id: 'notifications', name: '消息通知', icon: BellIcon, color: 'text-pink-600' },
  { id: 'system', name: '系统接口', icon: CogIcon, color: 'text-gray-600' }
]

// API示例数据
const examples = {
  auth: [
    {
      id: 'login',
      title: '用户登录',
      description: '使用邮箱和密码进行用户登录认证',
      method: 'POST',
      endpoint: '/api/v1/auth/login',
      request: {
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          email: '<EMAIL>',
          password: 'password123'
        }
      },
      response: {
        success: true,
        data: {
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          user: {
            id: 1,
            name: '张三',
            email: '<EMAIL>'
          }
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/auth/login'
data = {
    'email': '<EMAIL>',
    'password': 'password123'
}

response = requests.post(url, json=data)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/auth/login';
$data = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\\r\\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();
String json = "{\\"email\\":\\"<EMAIL>\\",\\"password\\":\\"password123\\"}";

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/auth/login"))
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(json))
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X POST "https://api.platform.com/v1/auth/login" \\
     -H "Content-Type: application/json" \\
     -d '{
       "email": "<EMAIL>",
       "password": "password123"
     }'`
      }
    },
    {
      id: 'register',
      title: '用户注册',
      description: '创建新的用户账号',
      method: 'POST',
      endpoint: '/api/v1/auth/register',
      request: {
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          name: '李四',
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123'
        }
      },
      response: {
        success: true,
        message: '注册成功',
        data: {
          user: {
            id: 2,
            name: '李四',
            email: '<EMAIL>',
            created_at: '2025-01-12T10:30:00Z'
          }
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: '李四',
    email: '<EMAIL>',
    password: 'password123',
    password_confirmation: 'password123'
  })
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/auth/register'
data = {
    'name': '李四',
    'email': '<EMAIL>',
    'password': 'password123',
    'password_confirmation': 'password123'
}

response = requests.post(url, json=data)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/auth/register';
$data = [
    'name' => '李四',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\\r\\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();
String json = "{\\"name\\":\\"李四\\",\\"email\\":\\"<EMAIL>\\",\\"password\\":\\"password123\\",\\"password_confirmation\\":\\"password123\\"}";

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/auth/register"))
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(json))
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X POST "https://api.platform.com/v1/auth/register" \\
     -H "Content-Type: application/json" \\
     -d '{
       "name": "李四",
       "email": "<EMAIL>",
       "password": "password123",
       "password_confirmation": "password123"
     }'`
      }
    }
  ],
  users: [
    {
      id: 'get-user',
      title: '获取用户信息',
      description: '根据用户ID获取用户详细信息',
      method: 'GET',
      endpoint: '/api/v1/users/{id}',
      request: {
        headers: {
          'Authorization': 'Bearer YOUR_API_KEY',
          'Content-Type': 'application/json'
        }
      },
      response: {
        success: true,
        data: {
          id: 1,
          name: '张三',
          email: '<EMAIL>',
          avatar: 'https://example.com/avatar.jpg',
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-12T10:30:00Z'
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/users/1', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/users/1'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

response = requests.get(url, headers=headers)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/users/1';

$options = [
    'http' => [
        'header' => "Authorization: Bearer YOUR_API_KEY\\r\\n" .
                   "Content-Type: application/json\\r\\n",
        'method' => 'GET'
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/users/1"))
    .header("Authorization", "Bearer YOUR_API_KEY")
    .header("Content-Type", "application/json")
    .GET()
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X GET "https://api.platform.com/v1/users/1" \\
     -H "Authorization: Bearer YOUR_API_KEY" \\
     -H "Content-Type: application/json"`
      }
    }
  ],
  files: [
    {
      id: 'upload-file',
      title: '文件上传',
      description: '上传文件到服务器',
      method: 'POST',
      endpoint: '/api/v1/files/upload',
      request: {
        headers: {
          'Authorization': 'Bearer YOUR_API_KEY',
          'Content-Type': 'multipart/form-data'
        },
        body: 'FormData with file'
      },
      response: {
        success: true,
        data: {
          id: 'file_123',
          filename: 'document.pdf',
          size: 1024000,
          url: 'https://cdn.platform.com/files/document.pdf',
          uploaded_at: '2025-01-12T10:30:00Z'
        }
      },
      codes: {
        javascript: `const formData = new FormData();
formData.append('file', fileInput.files[0]);

const response = await fetch('https://api.platform.com/v1/files/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/files/upload'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}

with open('document.pdf', 'rb') as file:
    files = {'file': file}
    response = requests.post(url, headers=headers, files=files)
    result = response.json()
    print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/files/upload';
$headers = [
    'Authorization: Bearer YOUR_API_KEY'
];

$file = new CURLFile('document.pdf', 'application/pdf', 'document.pdf');
$data = ['file' => $file];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);
print_r($result);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.nio.file.Path;

HttpClient client = HttpClient.newHttpClient();

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/files/upload"))
    .header("Authorization", "Bearer YOUR_API_KEY")
    .POST(HttpRequest.BodyPublishers.ofFile(Path.of("document.pdf")))
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X POST "https://api.platform.com/v1/files/upload" \\
     -H "Authorization: Bearer YOUR_API_KEY" \\
     -F "file=@document.pdf"`
      }
    }
  ],
  payments: [
    {
      id: 'create-payment',
      title: '创建支付订单',
      description: '创建新的支付订单',
      method: 'POST',
      endpoint: '/api/v1/payments',
      request: {
        headers: {
          'Authorization': 'Bearer YOUR_API_KEY',
          'Content-Type': 'application/json'
        },
        body: {
          amount: 9900,
          currency: 'CNY',
          description: 'API平台专业版套餐',
          customer_id: 'cust_123',
          return_url: 'https://yoursite.com/success'
        }
      },
      response: {
        success: true,
        data: {
          id: 'pay_456',
          amount: 9900,
          currency: 'CNY',
          status: 'pending',
          payment_url: 'https://pay.platform.com/pay_456',
          created_at: '2025-01-12T10:30:00Z'
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/payments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: 9900,
    currency: 'CNY',
    description: 'API平台专业版套餐',
    customer_id: 'cust_123',
    return_url: 'https://yoursite.com/success'
  })
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/payments'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}
data = {
    'amount': 9900,
    'currency': 'CNY',
    'description': 'API平台专业版套餐',
    'customer_id': 'cust_123',
    'return_url': 'https://yoursite.com/success'
}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/payments';
$data = [
    'amount' => 9900,
    'currency' => 'CNY',
    'description' => 'API平台专业版套餐',
    'customer_id' => 'cust_123',
    'return_url' => 'https://yoursite.com/success'
];

$options = [
    'http' => [
        'header' => "Authorization: Bearer YOUR_API_KEY\\r\\n" .
                   "Content-Type: application/json\\r\\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();
String json = "{\\"amount\\":9900,\\"currency\\":\\"CNY\\",\\"description\\":\\"API平台专业版套餐\\",\\"customer_id\\":\\"cust_123\\",\\"return_url\\":\\"https://yoursite.com/success\\"}";

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/payments"))
    .header("Authorization", "Bearer YOUR_API_KEY")
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(json))
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X POST "https://api.platform.com/v1/payments" \\
     -H "Authorization: Bearer YOUR_API_KEY" \\
     -H "Content-Type: application/json" \\
     -d '{
       "amount": 9900,
       "currency": "CNY",
       "description": "API平台专业版套餐",
       "customer_id": "cust_123",
       "return_url": "https://yoursite.com/success"
     }'`
      }
    }
  ],
  notifications: [
    {
      id: 'send-notification',
      title: '发送通知',
      description: '向用户发送推送通知',
      method: 'POST',
      endpoint: '/api/v1/notifications',
      request: {
        headers: {
          'Authorization': 'Bearer YOUR_API_KEY',
          'Content-Type': 'application/json'
        },
        body: {
          user_id: 123,
          title: '系统通知',
          message: '您的API调用次数即将达到限制',
          type: 'warning',
          channels: ['email', 'push']
        }
      },
      response: {
        success: true,
        data: {
          id: 'notif_789',
          status: 'sent',
          sent_at: '2025-01-12T10:30:00Z',
          channels_sent: ['email', 'push']
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/notifications', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 123,
    title: '系统通知',
    message: '您的API调用次数即将达到限制',
    type: 'warning',
    channels: ['email', 'push']
  })
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/notifications'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}
data = {
    'user_id': 123,
    'title': '系统通知',
    'message': '您的API调用次数即将达到限制',
    'type': 'warning',
    'channels': ['email', 'push']
}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/notifications';
$data = [
    'user_id' => 123,
    'title' => '系统通知',
    'message' => '您的API调用次数即将达到限制',
    'type' => 'warning',
    'channels' => ['email', 'push']
];

$options = [
    'http' => [
        'header' => "Authorization: Bearer YOUR_API_KEY\\r\\n" .
                   "Content-Type: application/json\\r\\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();
String json = "{\\"user_id\\":123,\\"title\\":\\"系统通知\\",\\"message\\":\\"您的API调用次数即将达到限制\\",\\"type\\":\\"warning\\",\\"channels\\":[\\"email\\",\\"push\\"]}";

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/notifications"))
    .header("Authorization", "Bearer YOUR_API_KEY")
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(json))
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X POST "https://api.platform.com/v1/notifications" \\
     -H "Authorization: Bearer YOUR_API_KEY" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_id": 123,
       "title": "系统通知",
       "message": "您的API调用次数即将达到限制",
       "type": "warning",
       "channels": ["email", "push"]
     }'`
      }
    }
  ],
  system: [
    {
      id: 'get-status',
      title: '系统状态',
      description: '获取API系统状态信息',
      method: 'GET',
      endpoint: '/api/v1/system/status',
      request: {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      response: {
        success: true,
        data: {
          status: 'operational',
          version: '2.1.0',
          uptime: 99.99,
          response_time: 45,
          last_updated: '2025-01-12T10:30:00Z'
        }
      },
      codes: {
        javascript: `const response = await fetch('https://api.platform.com/v1/system/status', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);`,
        python: `import requests

url = 'https://api.platform.com/v1/system/status'
headers = {
    'Content-Type': 'application/json'
}

response = requests.get(url, headers=headers)
result = response.json()
print(result)`,
        php: `<?php
$url = 'https://api.platform.com/v1/system/status';

$options = [
    'http' => [
        'header' => "Content-Type: application/json\\r\\n",
        'method' => 'GET'
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);
print_r($response);
?>`,
        java: `import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

HttpClient client = HttpClient.newHttpClient();

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.platform.com/v1/system/status"))
    .header("Content-Type", "application/json")
    .GET()
    .build();

HttpResponse<String> response = client.send(request,
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());`,
        curl: `curl -X GET "https://api.platform.com/v1/system/status" \\
     -H "Content-Type: application/json"`
      }
    }
  ]
}

// 计算属性
type ExampleMap = Record<string, Array<{ id: string } & Record<string, any>>>
const asMap = examples as unknown as ExampleMap

const getExamples = (key: string) => (asMap[key] || [])

const currentExamples = computed(() => {
  return getExamples(activeCategory.value)
})

// 复制代码到剪贴板
const copyCode = async (code: string, exampleId: string) => {
  try {
    await navigator.clipboard.writeText(code)
    copiedCode.value = exampleId
    toast.success('代码已复制到剪贴板')
    setTimeout(() => {
      copiedCode.value = ''
    }, 2000)
  } catch (error) {
    toast.error('复制失败，请手动复制')
  }
}

// 切换示例展开状态
const toggleExample = (exampleId: string) => {
  expandedExample.value = expandedExample.value === exampleId ? null : exampleId
}

// 运行示例（模拟）
const runExample = (example: any) => {
  toast.info(`正在运行示例: ${example.title}`)
  // 这里可以添加实际的API调用逻辑
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-white mb-4">API示例</h1>
          <p class="text-xl text-purple-100 max-w-3xl mx-auto">
            通过实际的代码示例学习如何使用我们的API。支持多种编程语言，包含完整的请求和响应示例。
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 左侧：分类导航 -->
        <div class="lg:w-64 flex-shrink-0">
          <div class="sticky top-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">API分类</h3>
              <nav class="space-y-2">
                <button
                  v-for="category in categories"
                  :key="category.id"
                  @click="activeCategory = category.id"
                  :class="[
                    'w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    activeCategory === category.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  ]"
                >
                  <component :is="category.icon" :class="['h-5 w-5 mr-3', category.color]" />
                  {{ category.name }}
                </button>
              </nav>
            </div>

            <!-- 编程语言选择 -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">编程语言</h3>
              <div class="space-y-2">
                <button
                  v-for="language in languages"
                  :key="language.id"
                  @click="activeLanguage = language.id"
                  :class="[
                    'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    activeLanguage === language.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  ]"
                >
                  <span>{{ language.name }}</span>
                  <span :class="['px-2 py-1 text-xs rounded-full', language.color]">
                    {{ language.name }}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：示例内容 -->
        <div class="flex-1">
          <div class="space-y-6">
            <div v-for="example in currentExamples" :key="example.id" class="bg-white rounded-xl shadow-sm border border-gray-200">
              <!-- 示例头部 -->
              <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                      <span :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        example.method === 'GET' ? 'bg-green-100 text-green-800' :
                        example.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                        example.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        example.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      ]">
                        {{ example.method }}
                      </span>
                      <code class="text-sm font-mono text-gray-600">{{ example.endpoint }}</code>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <button
                      @click="runExample(example)"
                      class="inline-flex items-center px-3 py-1 text-sm font-medium text-green-700 bg-green-100 rounded-lg hover:bg-green-200 transition-colors"
                    >
                      <PlayIcon class="h-4 w-4 mr-1" />
                      运行
                    </button>
                    <button
                      @click="toggleExample(example.id)"
                      class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <component
                        :is="expandedExample === example.id ? ChevronDownIcon : ChevronRightIcon"
                        class="h-4 w-4 mr-1"
                      />
                      {{ expandedExample === example.id ? '收起' : '展开' }}
                    </button>
                  </div>
                </div>
                <div class="mt-2">
                  <h3 class="text-lg font-semibold text-gray-900">{{ example.title }}</h3>
                  <p class="text-gray-600 mt-1">{{ example.description }}</p>
                </div>
              </div>

              <!-- 示例内容 -->
              <div v-if="expandedExample === example.id" class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- 请求示例 -->
                  <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-3">请求示例</h4>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-300">Headers</span>
                      </div>
                      <pre class="text-green-400 text-sm"><code>{{ JSON.stringify(example.request.headers, null, 2) }}</code></pre>
                      <div v-if="example.request.body" class="mt-4">
                        <div class="flex items-center justify-between mb-2">
                          <span class="text-sm font-medium text-gray-300">Body</span>
                        </div>
                        <pre class="text-blue-400 text-sm"><code>{{ JSON.stringify(example.request.body, null, 2) }}</code></pre>
                      </div>
                    </div>
                  </div>

                  <!-- 响应示例 -->
                  <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-3">响应示例</h4>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4">
                      <pre class="text-yellow-400 text-sm"><code>{{ JSON.stringify(example.response, null, 2) }}</code></pre>
                    </div>
                  </div>
                </div>

                <!-- 代码示例 -->
                <div class="mt-6">
                  <h4 class="text-md font-semibold text-gray-900 mb-3">代码示例 ({{ languages.find(l => l.id === activeLanguage)?.name }})</h4>
                  <div class="relative">
                    <div class="bg-gray-900 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-300">{{ languages.find(l => l.id === activeLanguage)?.name }}</span>
                        <button
                          @click="copyCode(example.codes[activeLanguage], example.id)"
                          class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-300 hover:text-white transition-colors"
                        >
                          <component
                            :is="copiedCode === example.id ? CheckIcon : DocumentDuplicateIcon"
                            class="h-4 w-4 mr-1"
                          />
                          {{ copiedCode === example.id ? '已复制' : '复制' }}
                        </button>
                      </div>
                      <pre class="text-gray-300 text-sm overflow-x-auto"><code>{{ example.codes[activeLanguage] }}</code></pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="currentExamples.length === 0" class="text-center py-12">
            <CodeBracketIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无示例</h3>
            <p class="text-gray-600">该分类下暂时没有可用的示例，请选择其他分类。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* API示例页面样式 */
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
