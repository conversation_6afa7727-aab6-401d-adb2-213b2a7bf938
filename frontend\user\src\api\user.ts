// Copyright (c) 2025 Aoki. All rights reserved.

import request from '@/utils/request'
import type {
  UserProfile,
  UserStats,
  ApiUsageStats,
  Bill,
  Plan,
  SecuritySettings,
  Notification,
  NotificationSettings,
  UpdateProfileData,
  ChangePasswordData,
  TwoFactorSetupData,
  UserProfileResponse,
  UserStatsResponse,
  BillsResponse,
  PlansResponse,
  NotificationsResponse
} from '@/types/user'

// 禁用模拟数据，使用真实API
const isDev = false

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const userApi = {
  // 获取用户个人信息
  getProfile: async (): Promise<UserProfileResponse> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        data: {
          id: 1,
          name: '张三',
          email: '<EMAIL>',
          avatar: 'https://ui-avatars.com/api/?name=张三&background=3b82f6&color=fff',
          phone: '+86 138****8888',
          company: 'ABC科技有限公司',
          website: 'https://example.com',
          bio: '全栈开发工程师，专注于API开发和微服务架构',
          location: '北京市朝阳区',
          timezone: 'Asia/Shanghai',
          email_verified_at: '2024-01-01T00:00:00Z',
          phone_verified_at: '2024-01-01T00:00:00Z',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z'
        },
        message: 'Success'
      }
    }
    return request.get('/user/profile')
  },

  // 更新用户个人信息
  updateProfile: async (data: UpdateProfileData): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(800)
      return {
        success: true,
        message: '个人信息更新成功'
      }
    }
    return request.put('/user/profile', data)
  },

  // 上传头像
  uploadAvatar: async (file: File): Promise<{ success: boolean; data: { avatar_url: string }; message: string }> => {
    if (isDev) {
      await delay(1000)
      return {
        success: true,
        data: {
          avatar_url: 'https://ui-avatars.com/api/?name=张三&background=10b981&color=fff'
        },
        message: '头像上传成功'
      }
    }
    const formData = new FormData()
    formData.append('avatar', file)
    return request.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 修改密码
  changePassword: async (data: ChangePasswordData): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(600)
      return {
        success: true,
        message: '密码修改成功'
      }
    }
    return request.put('/user/password', data)
  },

  // 获取用户统计信息（用于控制台）
  getUserStats: async (): Promise<{ success: boolean; data: any; message: string }> => {
    if (isDev) {
      await delay(600)
      return {
        success: true,
        data: {
          totalApiCalls: 1250,
          totalApiKeys: 3,
          favoriteApis: 8,
          lastLoginAt: new Date().toISOString()
        },
        message: 'Success'
      }
    }
    return request.get('/user/dashboard-stats')
  },

  // 获取API调用日志
  getApiCallLogs: async (params?: { limit?: number }): Promise<{ success: boolean; data: any[]; message: string }> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        data: [
          {
            id: 1,
            api_name: '用户登录',
            method: 'POST',
            endpoint: '/api/auth/login',
            status_code: 200,
            response_time: 150,
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            api_name: '获取用户信息',
            method: 'GET',
            endpoint: '/api/user/profile',
            status_code: 200,
            response_time: 85,
            created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
          }
        ],
        message: 'Success'
      }
    }
    return request.get('/user/api-call-logs', { params })
  },

  // 获取用户统计信息
  getStats: async (): Promise<UserStatsResponse> => {
    if (isDev) {
      await delay(600)
      return {
        success: true,
        data: {
          total_api_calls: 15420,
          total_api_keys: 3,
          total_favorites: 12,
          monthly_api_calls: 2340,
          daily_api_calls: 78,
          success_rate: 98.5,
          average_response_time: 145,
          most_used_api: {
            id: 1,
            name: '用户注册',
            calls: 5680
          }
        },
        message: 'Success'
      }
    }
    return request.get('/user/stats')
  },

  // 获取API使用统计
  getUsageStats: async (days = 30): Promise<{ success: boolean; data: ApiUsageStats[]; message: string }> => {
    if (isDev) {
      await delay(800)
      const stats: ApiUsageStats[] = []
      const now = new Date()
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        
        stats.push({
          date: date.toISOString().split('T')[0],
          api_calls: Math.floor(Math.random() * 200) + 50,
          success_calls: Math.floor(Math.random() * 180) + 45,
          failed_calls: Math.floor(Math.random() * 20) + 2,
          average_response_time: Math.floor(Math.random() * 100) + 100
        })
      }
      
      return {
        success: true,
        data: stats,
        message: 'Success'
      }
    }
    return request.get('/user/usage-stats', { params: { days } })
  },

  // 获取账单列表
  getBills: async (): Promise<BillsResponse> => {
    if (isDev) {
      await delay(700)
      return {
        success: true,
        data: [
          {
            id: 1,
            user_id: 1,
            amount: 99.00,
            currency: 'CNY',
            status: 'paid',
            billing_period_start: '2024-01-01T00:00:00Z',
            billing_period_end: '2024-01-31T23:59:59Z',
            items: [
              {
                id: 1,
                description: '专业版套餐 - 月付',
                quantity: 1,
                unit_price: 99.00,
                total_price: 99.00
              }
            ],
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          },
          {
            id: 2,
            user_id: 1,
            amount: 99.00,
            currency: 'CNY',
            status: 'pending',
            billing_period_start: '2024-02-01T00:00:00Z',
            billing_period_end: '2024-02-29T23:59:59Z',
            items: [
              {
                id: 2,
                description: '专业版套餐 - 月付',
                quantity: 1,
                unit_price: 99.00,
                total_price: 99.00
              }
            ],
            created_at: '2024-02-01T00:00:00Z',
            updated_at: '2024-02-01T00:00:00Z'
          }
        ],
        message: 'Success'
      }
    }
    return request.get('/user/bills')
  },

  // 获取套餐列表
  getPlans: async (): Promise<PlansResponse> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        data: [
          {
            id: 1,
            name: '免费版',
            description: '适合个人开发者和小型项目',
            price: 0,
            currency: 'CNY',
            billing_cycle: 'monthly',
            features: [
              { name: 'API调用', description: '每月1000次', included: true },
              { name: 'API密钥', description: '最多1个', included: true },
              { name: '基础支持', description: '社区支持', included: true },
              { name: '高级功能', description: '不包含', included: false }
            ],
            limits: {
              api_calls_per_month: 1000,
              api_keys_limit: 1,
              rate_limit_per_minute: 60,
              support_level: 'basic'
            },
            is_current: true
          },
          {
            id: 2,
            name: '专业版',
            description: '适合中小企业和团队开发',
            price: 99,
            currency: 'CNY',
            billing_cycle: 'monthly',
            features: [
              { name: 'API调用', description: '每月50000次', included: true },
              { name: 'API密钥', description: '最多5个', included: true },
              { name: '邮件支持', description: '工作日响应', included: true },
              { name: '高级功能', description: '包含', included: true }
            ],
            limits: {
              api_calls_per_month: 50000,
              api_keys_limit: 5,
              rate_limit_per_minute: 300,
              support_level: 'standard'
            },
            is_popular: true
          },
          {
            id: 3,
            name: '企业版',
            description: '适合大型企业和高并发场景',
            price: 299,
            currency: 'CNY',
            billing_cycle: 'monthly',
            features: [
              { name: 'API调用', description: '每月200000次', included: true },
              { name: 'API密钥', description: '无限制', included: true },
              { name: '专属支持', description: '7x24小时', included: true },
              { name: '定制功能', description: '支持定制', included: true }
            ],
            limits: {
              api_calls_per_month: 200000,
              api_keys_limit: -1, // -1 表示无限制
              rate_limit_per_minute: 1000,
              support_level: 'premium'
            }
          }
        ],
        message: 'Success'
      }
    }
    return request.get('/plans')
  },

  // 升级套餐
  upgradePlan: async (planId: number): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(1000)
      return {
        success: true,
        message: '套餐升级成功'
      }
    }
    return request.post('/user/upgrade-plan', { plan_id: planId })
  },

  // 获取安全设置
  getSecuritySettings: async (): Promise<{ success: boolean; data: SecuritySettings; message: string }> => {
    if (isDev) {
      await delay(600)
      return {
        success: true,
        data: {
          two_factor_enabled: false,
          login_notifications: true,
          api_key_notifications: true,
          password_last_changed: '2024-01-01T00:00:00Z',
          active_sessions: [
            {
              id: 'session_1',
              device: 'Windows PC',
              browser: 'Chrome 120.0',
              ip_address: '*************',
              location: '北京市朝阳区',
              last_activity: '2024-01-15T10:30:00Z',
              is_current: true
            },
            {
              id: 'session_2',
              device: 'iPhone 15',
              browser: 'Safari 17.0',
              ip_address: '*************',
              location: '北京市朝阳区',
              last_activity: '2024-01-14T18:45:00Z',
              is_current: false
            }
          ],
          login_history: [
            {
              id: 1,
              ip_address: '*************',
              user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              location: '北京市朝阳区',
              status: 'success',
              created_at: '2024-01-15T10:30:00Z'
            },
            {
              id: 2,
              ip_address: '*************',
              user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
              location: '北京市朝阳区',
              status: 'success',
              created_at: '2024-01-14T18:45:00Z'
            }
          ]
        },
        message: 'Success'
      }
    }
    return request.get('/user/security')
  },

  // 启用/禁用两步验证
  toggleTwoFactor: async (enabled: boolean): Promise<{ success: boolean; data?: TwoFactorSetupData; message: string }> => {
    if (isDev) {
      await delay(800)
      if (enabled) {
        return {
          success: true,
          data: {
            secret: 'JBSWY3DPEHPK3PXP',
            qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            backup_codes: [
              '12345678',
              '87654321',
              '11223344',
              '44332211',
              '55667788'
            ]
          },
          message: '两步验证设置成功'
        }
      } else {
        return {
          success: true,
          message: '两步验证已禁用'
        }
      }
    }
    return request.post('/user/two-factor', { enabled })
  },

  // 终止会话
  terminateSession: async (sessionId: string): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        message: '会话已终止'
      }
    }
    return request.delete(`/user/sessions/${sessionId}`)
  },

  // 获取通知列表
  getNotifications: async (page = 1, limit = 20): Promise<NotificationsResponse> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        data: [
          {
            id: 1,
            user_id: 1,
            type: 'info',
            title: '欢迎使用API平台',
            message: '感谢您注册我们的API平台，开始探索强大的API服务吧！',
            read_at: undefined,
            created_at: '2024-01-15T10:00:00Z'
          },
          {
            id: 2,
            user_id: 1,
            type: 'warning',
            title: 'API调用量提醒',
            message: '您本月的API调用量已达到80%，请注意使用情况。',
            read_at: '2024-01-14T15:30:00Z',
            created_at: '2024-01-14T14:00:00Z'
          },
          {
            id: 3,
            user_id: 1,
            type: 'success',
            title: '账单支付成功',
            message: '您的1月份账单已支付成功，金额：¥99.00',
            read_at: '2024-01-13T09:15:00Z',
            created_at: '2024-01-13T09:00:00Z'
          }
        ],
        message: 'Success'
      }
    }
    return request.get('/user/notifications', { params: { page, limit } })
  },

  // 标记通知为已读
  markNotificationRead: async (notificationId: number): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(300)
      return {
        success: true,
        message: '通知已标记为已读'
      }
    }
    return request.put(`/user/notifications/${notificationId}/read`)
  },

  // 标记所有通知为已读
  markAllNotificationsRead: async (): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        message: '所有通知已标记为已读'
      }
    }
    return request.put('/user/notifications/read-all')
  },

  // 获取通知设置
  getNotificationSettings: async (): Promise<{ success: boolean; data: NotificationSettings; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        data: {
          email_notifications: true,
          push_notifications: true,
          api_status_notifications: true,
          billing_notifications: true,
          security_notifications: true,
          marketing_notifications: false
        },
        message: 'Success'
      }
    }
    return request.get('/user/notification-settings')
  },

  // 更新通知设置
  updateNotificationSettings: async (settings: NotificationSettings): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        message: '通知设置更新成功'
      }
    }
    return request.put('/user/notification-settings', settings)
  }
}
