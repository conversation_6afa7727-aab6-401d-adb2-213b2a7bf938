// Copyright (c) 2025 Aoki. All rights reserved.

import type { 
  ApiCategory, 
  ApiEndpoint, 
  PaginatedResponse, 
  ApiStats,
  ApiRating,
  ApiFavorite
} from '@/types'

// 模拟分类数据
export const mockCategories: ApiCategory[] = [
  {
    id: 1,
    name: '用户管理',
    description: '用户注册、登录、资料管理相关API',
    icon: 'user',
    sort_order: 1,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '支付服务',
    description: '支付、订单、交易相关API',
    icon: 'credit-card',
    sort_order: 2,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    name: '消息通知',
    description: '短信、邮件、推送通知相关API',
    icon: 'bell',
    sort_order: 3,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    name: '文件存储',
    description: '文件上传、下载、管理相关API',
    icon: 'folder',
    sort_order: 4,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 5,
    name: '数据分析',
    description: '统计、报表、分析相关API',
    icon: 'chart-bar',
    sort_order: 5,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

// 模拟API数据
export const mockApis: ApiEndpoint[] = [
  {
    id: 1,
    category_id: 1,
    name: '用户注册',
    description: '创建新用户账户，支持邮箱和手机号注册',
    endpoint: '/api/auth/register',
    method: 'POST',
    version: '1.0',
    status: 'active',
    lastUpdated: '2024-01-01',
    is_public: true,
    is_active: true,
    rate_limit: 60,
    requires_auth: false,
    category: mockCategories[0],
    parameters: [
      {
        name: 'email',
        type: 'string',
        required: true,
        description: '用户邮箱地址',
        example: '<EMAIL>'
      },
      {
        name: 'password',
        type: 'string',
        required: true,
        description: '用户密码，至少8位',
        example: 'password123'
      },
      {
        name: 'name',
        type: 'string',
        required: true,
        description: '用户姓名',
        example: '张三'
      }
    ],
    responses: [
      {
        status_code: 201,
        description: '注册成功',
        example: {
          success: true,
          message: '注册成功',
          data: {
            user: {
              id: 1,
              email: '<EMAIL>',
              name: '张三'
            },
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
          }
        }
      },
      {
        status_code: 422,
        description: '验证失败',
        example: {
          success: false,
          message: '验证失败',
          errors: {
            email: ['邮箱格式不正确']
          }
        }
      }
    ],
    examples: [
      {
        name: '基本注册',
        description: '使用邮箱和密码注册新用户',
        request: {
          method: 'POST',
          url: '/api/auth/register',
          headers: {
            'Content-Type': 'application/json'
          },
          body: {
            email: '<EMAIL>',
            password: 'password123',
            name: '张三'
          }
        },
        response: {
          status: 201,
          body: {
            success: true,
            message: '注册成功',
            data: {
              user: {
                id: 1,
                email: '<EMAIL>',
                name: '张三'
              },
              token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
            }
          }
        }
      }
    ],
    total_calls: 15420,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    category_id: 1,
    name: '用户登录',
    description: '用户身份验证，获取访问令牌',
    endpoint: '/api/auth/login',
    method: 'POST',
    version: '1.0',
    is_public: true,
    is_active: true,
    rate_limit: 30,
    requires_auth: false,
    category: mockCategories[0],
    parameters: [
      {
        name: 'email',
        type: 'string',
        required: true,
        description: '用户邮箱地址',
        example: '<EMAIL>'
      },
      {
        name: 'password',
        type: 'string',
        required: true,
        description: '用户密码',
        example: 'password123'
      }
    ],
    responses: [
      {
        status_code: 200,
        description: '登录成功',
        example: {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: 1,
              email: '<EMAIL>',
              name: '张三'
            },
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
          }
        }
      }
    ],
    examples: [],
    total_calls: 12850,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    category_id: 2,
    name: '创建支付订单',
    description: '创建新的支付订单，支持多种支付方式',
    endpoint: '/api/payments/orders',
    method: 'POST',
    version: '1.0',
    is_public: false,
    is_active: true,
    rate_limit: 100,
    requires_auth: true,
    category: mockCategories[1],
    parameters: [
      {
        name: 'amount',
        type: 'number',
        required: true,
        description: '支付金额（分）',
        example: 10000
      },
      {
        name: 'currency',
        type: 'string',
        required: false,
        description: '货币类型',
        example: 'CNY'
      }
    ],
    responses: [
      {
        status_code: 201,
        description: '订单创建成功',
        example: {
          success: true,
          data: {
            order_id: 'ord_123456',
            amount: 10000,
            status: 'pending'
          }
        }
      }
    ],
    examples: [],
    total_calls: 8960,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z'
  },
  {
    id: 4,
    category_id: 3,
    name: '发送短信验证码',
    description: '发送短信验证码到指定手机号',
    endpoint: '/api/sms/send-code',
    method: 'POST',
    version: '1.0',
    is_public: true,
    is_active: true,
    rate_limit: 10,
    requires_auth: false,
    category: mockCategories[2],
    parameters: [
      {
        name: 'phone',
        type: 'string',
        required: true,
        description: '手机号码',
        example: '13800138000'
      },
      {
        name: 'type',
        type: 'string',
        required: false,
        description: '验证码类型',
        example: 'register'
      }
    ],
    responses: [
      {
        status_code: 200,
        description: '发送成功',
        example: {
          success: true,
          message: '验证码已发送',
          data: {
            code_id: 'sms_123456'
          }
        }
      }
    ],
    examples: [],
    total_calls: 25680,
    created_at: '2024-01-04T00:00:00Z',
    updated_at: '2024-01-04T00:00:00Z'
  },
  {
    id: 5,
    category_id: 4,
    name: '文件上传',
    description: '上传文件到云存储',
    endpoint: '/api/files/upload',
    method: 'POST',
    version: '1.0',
    is_public: false,
    is_active: true,
    rate_limit: 50,
    requires_auth: true,
    category: mockCategories[3],
    parameters: [
      {
        name: 'file',
        type: 'object',
        required: true,
        description: '要上传的文件',
        example: 'multipart/form-data'
      },
      {
        name: 'folder',
        type: 'string',
        required: false,
        description: '存储文件夹',
        example: 'uploads'
      }
    ],
    responses: [
      {
        status_code: 200,
        description: '上传成功',
        example: {
          success: true,
          data: {
            file_id: 'file_123456',
            url: 'https://cdn.example.com/file_123456.jpg'
          }
        }
      }
    ],
    examples: [],
    total_calls: 18750,
    created_at: '2024-01-05T00:00:00Z',
    updated_at: '2024-01-05T00:00:00Z'
  },
  {
    id: 6,
    category_id: 5,
    name: '获取用户统计',
    description: '获取用户行为统计数据',
    endpoint: '/api/analytics/user-stats',
    method: 'GET',
    version: '1.0',
    is_public: false,
    is_active: true,
    rate_limit: 100,
    requires_auth: true,
    category: mockCategories[4],
    parameters: [
      {
        name: 'start_date',
        type: 'string',
        required: false,
        description: '开始日期',
        example: '2024-01-01'
      },
      {
        name: 'end_date',
        type: 'string',
        required: false,
        description: '结束日期',
        example: '2024-01-31'
      }
    ],
    responses: [
      {
        status_code: 200,
        description: '获取成功',
        example: {
          success: true,
          data: {
            total_users: 10000,
            active_users: 8500,
            new_users: 1200
          }
        }
      }
    ],
    examples: [],
    total_calls: 9420,
    created_at: '2024-01-06T00:00:00Z',
    updated_at: '2024-01-06T00:00:00Z'
  }
]

// 模拟统计数据
export const mockStats: ApiStats = {
  total_apis: 156,
  public_apis: 89,
  categories_count: 5,
  average_rating: 4.2,
  total_favorites: 1234
}

// 模拟评价数据
export const mockRatings: ApiRating[] = [
  {
    id: 1,
    user_id: 1,
    api_id: 1,
    rating: 5,
    comment: '非常好用的API，文档清晰，响应速度快！',
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
    user: {
      id: 1,
      name: '开发者A',
      avatar: 'https://ui-avatars.com/api/?name=开发者A'
    }
  },
  {
    id: 2,
    user_id: 2,
    api_id: 1,
    rating: 4,
    comment: '功能完善，但希望能增加更多示例',
    created_at: '2024-01-11T00:00:00Z',
    updated_at: '2024-01-11T00:00:00Z',
    user: {
      id: 2,
      name: '开发者B',
      avatar: 'https://ui-avatars.com/api/?name=开发者B'
    }
  }
]

// 模拟收藏数据
export const mockFavorites: ApiFavorite[] = [
  {
    id: 1,
    user_id: 1,
    api_id: 1,
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z'
  }
]
