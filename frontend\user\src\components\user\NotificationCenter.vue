<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useToast } from 'vue-toastification'
import { 
  BellIcon,
  CheckIcon,
  XMarkIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'
import { userApi } from '@/api/user'
import type { Notification } from '@/types/user'

const toast = useToast()

const loading = ref(false)
const notifications = ref<Notification[]>([])
const showSettings = ref(false)

// 通知设置
const notificationSettings = ref({
  email_notifications: true,
  push_notifications: true,
  api_status_notifications: true,
  billing_notifications: true,
  security_notifications: true,
  marketing_notifications: false
})

// 计算属性
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read_at).length
})

const groupedNotifications = computed(() => {
  const groups: { [key: string]: Notification[] } = {}
  
  notifications.value.forEach(notification => {
    const date = new Date(notification.created_at).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(notification)
  })
  
  return Object.entries(groups).sort(([a], [b]) => 
    new Date(b).getTime() - new Date(a).getTime()
  )
})

// 获取通知图标
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'info':
      return InformationCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'error':
      return ExclamationCircleIcon
    case 'success':
      return CheckCircleIcon
    default:
      return BellIcon
  }
}

// 获取通知样式
const getNotificationClass = (type: string) => {
  switch (type) {
    case 'info':
      return 'text-blue-600 bg-blue-100'
    case 'warning':
      return 'text-yellow-600 bg-yellow-100'
    case 'error':
      return 'text-red-600 bg-red-100'
    case 'success':
      return 'text-green-600 bg-green-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

// 加载通知列表
const loadNotifications = async () => {
  try {
    loading.value = true
    const response = await userApi.getNotifications()
    if (response.success) {
      notifications.value = response.data
    }
  } catch (error) {
    console.error('Failed to load notifications:', error)
    toast.error('加载通知失败')
  } finally {
    loading.value = false
  }
}

// 加载通知设置
const loadNotificationSettings = async () => {
  try {
    const response = await userApi.getNotificationSettings()
    if (response.success) {
      notificationSettings.value = response.data
    }
  } catch (error) {
    console.error('Failed to load notification settings:', error)
  }
}

// 标记通知为已读
const markAsRead = async (notification: Notification) => {
  if (notification.read_at) return
  
  try {
    await userApi.markNotificationRead(notification.id)
    notification.read_at = new Date().toISOString()
  } catch (error) {
    console.error('Failed to mark notification as read:', error)
    toast.error('标记已读失败')
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    await userApi.markAllNotificationsRead()
    notifications.value.forEach(notification => {
      if (!notification.read_at) {
        notification.read_at = new Date().toISOString()
      }
    })
    toast.success('所有通知已标记为已读')
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error)
    toast.error('操作失败')
  }
}

// 更新通知设置
const updateNotificationSettings = async () => {
  try {
    await userApi.updateNotificationSettings(notificationSettings.value)
    toast.success('通知设置更新成功')
    showSettings.value = false
  } catch (error) {
    console.error('Failed to update notification settings:', error)
    toast.error('设置更新失败')
  }
}

// 格式化时间
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatGroupDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadNotifications()
  loadNotificationSettings()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 标题和操作 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <BellIcon class="w-6 h-6 text-primary-600" />
        <div>
          <h3 class="text-lg font-semibold text-gray-900">消息通知</h3>
          <p class="text-sm text-gray-600">
            {{ unreadCount > 0 ? `${unreadCount} 条未读消息` : '所有消息已读' }}
          </p>
        </div>
      </div>
      
      <div class="flex space-x-2">
        <button
          v-if="unreadCount > 0"
          @click="markAllAsRead"
          class="btn-outline btn-sm"
        >
          <CheckIcon class="w-4 h-4 mr-1" />
          全部已读
        </button>
        <button
          @click="showSettings = !showSettings"
          class="btn-outline btn-sm"
        >
          设置
        </button>
      </div>
    </div>

    <!-- 通知设置 -->
    <div v-if="showSettings" class="bg-gray-50 rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">通知设置</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.email_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">邮件通知</div>
            <div class="text-xs text-gray-500">通过邮件接收重要通知</div>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.push_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">推送通知</div>
            <div class="text-xs text-gray-500">浏览器推送通知</div>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.api_status_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">API状态通知</div>
            <div class="text-xs text-gray-500">API服务状态变更通知</div>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.billing_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">账单通知</div>
            <div class="text-xs text-gray-500">账单和付款相关通知</div>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.security_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">安全通知</div>
            <div class="text-xs text-gray-500">账户安全相关通知</div>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            v-model="notificationSettings.marketing_notifications"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <div>
            <div class="text-sm font-medium text-gray-900">营销通知</div>
            <div class="text-xs text-gray-500">产品更新和营销信息</div>
          </div>
        </label>
      </div>
      
      <div class="flex justify-end mt-4">
        <button
          @click="updateNotificationSettings"
          class="btn-primary"
        >
          保存设置
        </button>
      </div>
    </div>

    <!-- 通知列表 -->
    <div v-if="loading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"></div>
      <p class="text-gray-600">加载中...</p>
    </div>
    
    <div v-else-if="notifications.length === 0" class="text-center py-12">
      <BellIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无通知</h3>
      <p class="text-gray-600">您的通知将显示在这里</p>
    </div>
    
    <div v-else class="space-y-6">
      <div
        v-for="[date, dayNotifications] in groupedNotifications"
        :key="date"
        class="space-y-3"
      >
        <h4 class="text-sm font-medium text-gray-500 sticky top-0 bg-gray-50 py-2">
          {{ formatGroupDate(date) }}
        </h4>
        
        <div class="space-y-2">
          <div
            v-for="notification in dayNotifications"
            :key="notification.id"
            @click="markAsRead(notification)"
            :class="[
              'p-4 rounded-lg border cursor-pointer transition-colors',
              notification.read_at 
                ? 'bg-white border-gray-200 hover:bg-gray-50' 
                : 'bg-blue-50 border-blue-200 hover:bg-blue-100'
            ]"
          >
            <div class="flex items-start space-x-3">
              <div :class="['p-2 rounded-lg', getNotificationClass(notification.type)]">
                <component :is="getNotificationIcon(notification.type)" class="w-5 h-5" />
              </div>
              
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <h5 class="text-sm font-medium text-gray-900 truncate">
                    {{ notification.title }}
                  </h5>
                  <span class="text-xs text-gray-500 ml-2 flex-shrink-0">
                    {{ formatDate(notification.created_at) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600">{{ notification.message }}</p>
                
                <div v-if="!notification.read_at" class="mt-2">
                  <span class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-sm {
  @apply px-2 py-1 text-xs;
}
</style>
