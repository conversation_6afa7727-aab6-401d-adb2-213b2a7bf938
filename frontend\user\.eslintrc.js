module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    'plugin:vue/vue3-essential'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    'no-unused-vars': 'warn',
    'vue/multi-word-component-names': 'off'
  },
  overrides: [
    {
      files: ['tailwind.config.js'],
      env: {
        node: true
      },
      rules: {
        'no-undef': 'off'
      }
    }
  ]
}
