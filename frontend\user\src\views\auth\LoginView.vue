<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { LoginCredentials } from '@/types/auth'

const authStore = useAuthStore()

const form = reactive<LoginCredentials>({
  email: '',
  password: '',
  remember: false
})

const errors = ref<Record<string, string[]>>({})

const handleSubmit = async () => {
  errors.value = {}
  
  // 基本验证
  if (!form.email) {
    errors.value.email = ['邮箱不能为空']
  }
  if (!form.password) {
    errors.value.password = ['密码不能为空']
  }
  
  if (Object.keys(errors.value).length > 0) {
    return
  }
  
  await authStore.login(form)
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">API</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          登录您的账户
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          还没有账户？
          <RouterLink to="/auth/register" class="font-medium text-primary-600 hover:text-primary-500">
            立即注册
          </RouterLink>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="space-y-4">
          <div>
            <label for="email" class="form-label">邮箱地址</label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="form-input"
              :class="{ 'border-error-500 focus:border-error-500 focus:ring-error-500': errors.email }"
              placeholder="请输入邮箱地址"
            />
            <p v-if="errors.email" class="form-error">{{ errors.email[0] }}</p>
          </div>
          
          <div>
            <label for="password" class="form-label">密码</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="form-input"
              :class="{ 'border-error-500 focus:border-error-500 focus:ring-error-500': errors.password }"
              placeholder="请输入密码"
            />
            <p v-if="errors.password" class="form-error">{{ errors.password[0] }}</p>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember"
              v-model="form.remember"
              name="remember"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="remember" class="ml-2 block text-sm text-gray-900">
              记住我
            </label>
          </div>

          <div class="text-sm">
            <RouterLink to="/auth/forgot-password" class="font-medium text-primary-600 hover:text-primary-500">
              忘记密码？
            </RouterLink>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="authStore.loading"
            class="btn-primary w-full"
          >
            <span v-if="authStore.loading">登录中...</span>
            <span v-else>登录</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
/* Login page specific styles */
</style>
