<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'

const route = useRoute()
const loading = ref(true)
const api = ref<any>(null)
const error = ref<string>('')

// 获取API详情
const loadApiDetail = async () => {
  try {
    loading.value = true
    error.value = ''
    const apiId = route.params.id as string

    // 调用真实的后端API
    const response = await axios.get(`/api/apis/${apiId}`)

    if (response.data.success) {
      api.value = response.data.data.api || response.data.data
    } else {
      error.value = response.data.message || 'API不存在'
    }
  } catch (err: any) {
    console.error('Failed to load API detail:', err)
    if (err.response?.status === 404) {
      error.value = 'API不存在或已被删除'
    } else {
      error.value = '加载API详情失败，请稍后重试'
    }
    console.error('API Error:', error.value)
  } finally {
    loading.value = false
  }
}

// 辅助函数
const getMethodColor = (method: string) => {
  const colors: Record<string, string> = {
    'GET': 'bg-green-100 text-green-800',
    'POST': 'bg-blue-100 text-blue-800',
    'PUT': 'bg-yellow-100 text-yellow-800',
    'DELETE': 'bg-red-100 text-red-800',
    'PATCH': 'bg-purple-100 text-purple-800'
  }
  return colors[method?.toUpperCase()] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString('zh-CN')
  } catch {
    return dateString
  }
}

const formatJson = (jsonString: string | object) => {
  try {
    if (typeof jsonString === 'string') {
      return JSON.stringify(JSON.parse(jsonString), null, 2)
    } else {
      return JSON.stringify(jsonString, null, 2)
    }
  } catch {
    return jsonString?.toString() || 'N/A'
  }
}

onMounted(() => {
  loadApiDetail()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-lg text-gray-600">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 text-6xl mb-4">⚠️</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ error }}</h2>
        <div class="space-x-4">
          <button
            @click="loadApiDetail"
            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            重新加载
          </button>
          <router-link
            to="/apis"
            class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            返回API列表
          </router-link>
        </div>
      </div>

      <!-- API详情 -->
      <div v-else-if="api" class="space-y-6">
        <!-- 返回按钮 -->
        <div class="mb-4">
          <router-link
            to="/apis"
            class="inline-flex items-center text-blue-600 hover:text-blue-800"
          >
            ← 返回API列表
          </router-link>
        </div>

        <!-- API头部信息 -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ api.name || 'API名称' }}</h1>
              <p class="text-gray-600 mb-4">{{ api.description || '暂无描述' }}</p>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-gray-50 p-3 rounded">
                  <div class="text-sm text-gray-500">请求方法</div>
                  <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    {{ api.method || 'GET' }}
                  </span>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                  <div class="text-sm text-gray-500">端点</div>
                  <code class="text-sm bg-gray-200 px-2 py-1 rounded">{{ api.endpoint_url || api.endpoint || 'N/A' }}</code>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                  <div class="text-sm text-gray-500">版本</div>
                  <div class="font-medium">{{ api.version || '1.0.0' }}</div>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                  <div class="text-sm text-gray-500">状态</div>
                  <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    {{ api.status || '活跃' }}
                  </span>
                </div>
              </div>

              <div class="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                <span v-if="api.call_count !== undefined">查看次数: {{ api.call_count }}</span>
                <span v-if="api.author">作者: {{ api.author }}</span>
                <span v-if="api.updated_at">更新时间: {{ formatDate(api.updated_at) }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-3">
              <button class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <span>🚀</span>
                <span>试用API</span>
              </button>
            </div>
          </div>
        </div>

        <!-- API文档 -->
        <div v-if="api.documentation" class="bg-white shadow rounded-lg p-6">
          <h2 class="text-xl font-bold text-gray-900 mb-4">📄 API文档</h2>
          <div class="prose max-w-none">
            <div class="whitespace-pre-wrap text-gray-700">{{ api.documentation }}</div>
          </div>
        </div>

        <!-- 代码示例 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 请求示例 -->
          <div v-if="api.example_request" class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">💻 请求示例</h3>
            <pre class="bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto text-sm"><code>{{ api.example_request }}</code></pre>
          </div>

          <!-- 响应示例 -->
          <div v-if="api.example_response" class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">📋 响应示例</h3>
            <pre class="bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto text-sm"><code>{{ api.example_response }}</code></pre>
          </div>
        </div>

        <!-- API参数和响应结构 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 请求参数 -->
          <div v-if="api.request_schema" class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">📝 请求参数</h3>
            <pre class="bg-gray-50 p-4 rounded overflow-x-auto text-sm"><code>{{ formatJson(api.request_schema) }}</code></pre>
          </div>

          <!-- 响应结构 -->
          <div v-if="api.response_schema" class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">📊 响应结构</h3>
            <pre class="bg-gray-50 p-4 rounded overflow-x-auto text-sm"><code>{{ formatJson(api.response_schema) }}</code></pre>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
