<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  BookOpenIcon,
  CodeBracketIcon,
  KeyIcon,
  ShieldCheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  DocumentTextIcon,
  CubeIcon,
  CommandLineIcon
} from '@heroicons/vue/24/outline'

// 状态管理
const activeSection = ref('getting-started')

// 导航菜单
const navigation = ref([
  {
    id: 'getting-started',
    name: '快速开始',
    icon: BookOpenIcon,
    children: [
      { id: 'introduction', name: '介绍' },
      { id: 'authentication', name: '认证' },
      { id: 'rate-limiting', name: '限流' },
      { id: 'errors', name: '错误处理' }
    ]
  },
  {
    id: 'api-reference',
    name: 'API参考',
    icon: CodeBracketIcon,
    children: [
      { id: 'users', name: '用户API' },
      { id: 'auth', name: '认证API' },
      { id: 'files', name: '文件API' },
      { id: 'payments', name: '支付API' }
    ]
  },
  {
    id: 'sdks',
    name: 'SDK和工具',
    icon: CubeIcon,
    children: [
      { id: 'javascript', name: 'JavaScript SDK' },
      { id: 'python', name: 'Python SDK' },
      { id: 'php', name: 'PHP SDK' },
      { id: 'postman', name: 'Postman集合' }
    ]
  },
  {
    id: 'guides',
    name: '使用指南',
    icon: DocumentTextIcon,
    children: [
      { id: 'webhooks', name: 'Webhooks' },
      { id: 'pagination', name: '分页' },
      { id: 'filtering', name: '筛选和排序' },
      { id: 'best-practices', name: '最佳实践' }
    ]
  }
])

// 方法
const scrollToSection = (sectionId: string) => {
  activeSection.value = sectionId
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 生命周期
onMounted(() => {
  // 监听滚动事件来更新活跃的导航项
  const handleScroll = () => {
    const sections = document.querySelectorAll('[data-section]')
    let current = ''
    
    sections.forEach((section) => {
      const rect = section.getBoundingClientRect()
      if (rect.top <= 100) {
        current = section.getAttribute('data-section') || ''
      }
    })
    
    if (current) {
      activeSection.value = current
    }
  }
  
  window.addEventListener('scroll', handleScroll)
  
  return () => {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-gray-900">开发文档</h1>
          <p class="mt-4 text-lg text-gray-600">
            完整的API文档、SDK和使用指南，帮助您快速集成我们的服务
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 侧边栏导航 -->
        <div class="lg:w-64 flex-shrink-0">
          <div class="sticky top-8">
            <nav class="bg-white rounded-lg shadow p-6">
              <div class="space-y-4">
                <div v-for="section in navigation" :key="section.id">
                  <div class="flex items-center space-x-2 mb-2">
                    <component :is="section.icon" class="h-5 w-5 text-gray-400" />
                    <h3 class="text-sm font-medium text-gray-900">{{ section.name }}</h3>
                  </div>
                  <ul class="ml-7 space-y-1">
                    <li v-for="child in section.children" :key="child.id">
                      <button
                        @click="scrollToSection(child.id)"
                        :class="[
                          'block w-full text-left px-2 py-1 text-sm rounded transition-colors',
                          activeSection === child.id
                            ? 'text-primary-700 bg-primary-50'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                        ]"
                      >
                        {{ child.name }}
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1">
          <div class="bg-white rounded-lg shadow">
            <div class="p-8">
              <!-- 快速开始 -->
              <section id="introduction" data-section="introduction" class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🚀 平台介绍</h2>
                <div class="prose max-w-none">
                  <p class="text-lg text-gray-700 mb-6 leading-relaxed">
                    欢迎使用API平台！我们是一个专业的API服务聚合平台，致力于为开发者提供高质量、易集成的API服务。
                    无论您是个人开发者还是企业团队，都能在这里找到适合的API解决方案。
                  </p>

                  <!-- 核心优势 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                      <div class="flex items-start">
                        <CheckCircleIcon class="h-6 w-6 text-blue-600 mt-1 mr-3 flex-shrink-0" />
                        <div>
                          <h4 class="text-lg font-semibold text-blue-900 mb-3">技术特性</h4>
                          <ul class="text-sm text-blue-800 space-y-2">
                            <li>• RESTful API设计，遵循行业标准</li>
                            <li>• JSON格式数据交换，轻量高效</li>
                            <li>• HTTPS加密传输，保障数据安全</li>
                            <li>• 支持CORS跨域请求</li>
                            <li>• 完整的OpenAPI 3.0规范文档</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                      <div class="flex items-start">
                        <ShieldCheckIcon class="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                        <div>
                          <h4 class="text-lg font-semibold text-green-900 mb-3">服务保障</h4>
                          <ul class="text-sm text-green-800 space-y-2">
                            <li>• 99.9%服务可用性保证</li>
                            <li>• 24/7实时监控和告警</li>
                            <li>• 多地域容灾备份</li>
                            <li>• 专业技术支持团队</li>
                            <li>• 详细的API使用分析</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 快速开始步骤 -->
                  <div class="bg-gray-50 rounded-xl p-6 mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">🎯 5分钟快速开始</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div class="text-center">
                        <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">1</div>
                        <h5 class="font-medium text-gray-900 mb-2">注册账号</h5>
                        <p class="text-sm text-gray-600">免费注册开发者账号</p>
                      </div>
                      <div class="text-center">
                        <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">2</div>
                        <h5 class="font-medium text-gray-900 mb-2">获取API密钥</h5>
                        <p class="text-sm text-gray-600">在控制台生成API密钥</p>
                      </div>
                      <div class="text-center">
                        <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">3</div>
                        <h5 class="font-medium text-gray-900 mb-2">开始调用</h5>
                        <p class="text-sm text-gray-600">使用API密钥调用接口</p>
                      </div>
                    </div>
                  </div>

                  <!-- 支持的编程语言 -->
                  <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">💻 支持的编程语言</h4>
                    <div class="flex flex-wrap gap-3">
                      <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">JavaScript</span>
                      <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Python</span>
                      <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">PHP</span>
                      <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">Java</span>
                      <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">Node.js</span>
                      <span class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium">Go</span>
                      <span class="px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm font-medium">Ruby</span>
                      <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">C#</span>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 认证 -->
              <section id="authentication" data-section="authentication" class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🔐 API认证</h2>
                <div class="prose max-w-none">
                  <p class="text-lg text-gray-700 mb-6">
                    我们使用Bearer Token认证机制，确保API调用的安全性。每个请求都需要在HTTP头中包含有效的API密钥。
                  </p>

                  <!-- 认证方式 -->
                  <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
                    <h4 class="text-lg font-semibold text-blue-900 mb-4">认证方式</h4>
                    <p class="text-blue-800 mb-4">在每个API请求的Header中添加Authorization字段：</p>
                    <div class="bg-gray-900 rounded-lg p-4">
                      <pre class="text-green-400 text-sm"><code>Authorization: Bearer YOUR_API_KEY
Content-Type: application/json</code></pre>
                    </div>
                  </div>

                  <!-- 完整示例 -->
                  <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">📝 完整调用示例</h4>

                    <!-- cURL示例 -->
                    <div class="mb-4">
                      <h5 class="text-md font-medium text-gray-800 mb-2">cURL</h5>
                      <div class="bg-gray-900 rounded-lg p-4">
                        <pre class="text-green-400 text-sm"><code>curl -X GET "https://api.platform.com/v1/users/123" \
     -H "Authorization: Bearer sk_live_abc123def456ghi789" \
     -H "Content-Type: application/json"</code></pre>
                      </div>
                    </div>

                    <!-- JavaScript示例 -->
                    <div class="mb-4">
                      <h5 class="text-md font-medium text-gray-800 mb-2">JavaScript (Fetch)</h5>
                      <div class="bg-gray-900 rounded-lg p-4">
                        <pre class="text-blue-400 text-sm"><code>const response = await fetch('https://api.platform.com/v1/users/123', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer sk_live_abc123def456ghi789',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);</code></pre>
                      </div>
                    </div>

                    <!-- Python示例 -->
                    <div class="mb-4">
                      <h5 class="text-md font-medium text-gray-800 mb-2">Python (requests)</h5>
                      <div class="bg-gray-900 rounded-lg p-4">
                        <pre class="text-yellow-400 text-sm"><code>import requests

headers = {
    'Authorization': 'Bearer sk_live_abc123def456ghi789',
    'Content-Type': 'application/json'
}

response = requests.get('https://api.platform.com/v1/users/123', headers=headers)
data = response.json()
print(data)</code></pre>
                      </div>
                    </div>
                  </div>

                  <!-- 获取API密钥 -->
                  <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mb-6">
                    <h4 class="text-lg font-semibold text-green-900 mb-4">🔑 如何获取API密钥</h4>
                    <ol class="text-green-800 space-y-2">
                      <li><strong>1.</strong> 登录您的开发者控制台</li>
                      <li><strong>2.</strong> 进入"API密钥管理"页面</li>
                      <li><strong>3.</strong> 点击"生成新密钥"按钮</li>
                      <li><strong>4.</strong> 为密钥设置名称和权限范围</li>
                      <li><strong>5.</strong> 复制并安全保存生成的密钥</li>
                    </ol>
                    <div class="mt-4">
                      <a href="/developer/keys" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        前往密钥管理 →
                      </a>
                    </div>
                  </div>

                  <!-- 安全最佳实践 -->
                  <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <div class="flex items-start">
                      <ExclamationTriangleIcon class="h-6 w-6 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                      <div>
                        <h4 class="text-lg font-semibold text-yellow-900 mb-3">🛡️ 安全最佳实践</h4>
                        <ul class="text-yellow-800 space-y-2">
                          <li><strong>•</strong> 永远不要在客户端代码中硬编码API密钥</li>
                          <li><strong>•</strong> 使用环境变量存储API密钥</li>
                          <li><strong>•</strong> 定期轮换API密钥</li>
                          <li><strong>•</strong> 为不同环境使用不同的密钥</li>
                          <li><strong>•</strong> 监控API密钥的使用情况</li>
                          <li><strong>•</strong> 如果密钥泄露，立即撤销并重新生成</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 限流 -->
              <section id="rate-limiting" data-section="rate-limiting" class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">⚡ API限流策略</h2>
                <div class="prose max-w-none">
                  <p class="text-lg text-gray-700 mb-6">
                    为了确保服务的稳定性和公平性，我们对API请求实施了智能限流策略。不同套餐享有不同的请求配额。
                  </p>

                  <!-- 限流策略表格 -->
                  <div class="bg-white border border-gray-200 rounded-xl overflow-hidden mb-6">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                      <h4 class="text-lg font-semibold text-gray-900">套餐配额对比</h4>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">套餐类型</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">每分钟请求</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">每小时请求</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">每月请求</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">并发连接</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">价格</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                              <div class="flex items-center">
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full mr-2">免费版</span>
                              </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 font-medium">100</td>
                            <td class="px-6 py-4 text-sm text-gray-600">6,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">10,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">5</td>
                            <td class="px-6 py-4 text-sm font-medium text-green-600">免费</td>
                          </tr>
                          <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                              <div class="flex items-center">
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full mr-2">专业版</span>
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">推荐</span>
                              </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 font-medium">1,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">60,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">100,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">50</td>
                            <td class="px-6 py-4 text-sm font-medium text-blue-600">¥99/月</td>
                          </tr>
                          <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                              <div class="flex items-center">
                                <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full mr-2">企业版</span>
                              </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 font-medium">10,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">600,000</td>
                            <td class="px-6 py-4 text-sm text-gray-600">无限制</td>
                            <td class="px-6 py-4 text-sm text-gray-600">500</td>
                            <td class="px-6 py-4 text-sm font-medium text-purple-600">¥999/月</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- 限流机制说明 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                      <h4 class="text-lg font-semibold text-blue-900 mb-3">🔄 限流算法</h4>
                      <ul class="text-blue-800 space-y-2 text-sm">
                        <li><strong>•</strong> 令牌桶算法，支持突发流量</li>
                        <li><strong>•</strong> 滑动窗口计数，精确控制</li>
                        <li><strong>•</strong> 分布式限流，多节点同步</li>
                        <li><strong>•</strong> 智能降级，保障核心服务</li>
                      </ul>
                    </div>

                    <div class="bg-orange-50 border border-orange-200 rounded-xl p-6">
                      <h4 class="text-lg font-semibold text-orange-900 mb-3">📊 限流响应</h4>
                      <div class="text-orange-800 text-sm space-y-2">
                        <p><strong>HTTP状态码：</strong> 429 Too Many Requests</p>
                        <p><strong>响应头：</strong></p>
                        <ul class="ml-4 space-y-1">
                          <li>• X-RateLimit-Limit: 1000</li>
                          <li>• X-RateLimit-Remaining: 0</li>
                          <li>• X-RateLimit-Reset: 1640995200</li>
                          <li>• Retry-After: 60</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- 最佳实践 -->
                  <div class="bg-green-50 border border-green-200 rounded-xl p-6">
                    <h4 class="text-lg font-semibold text-green-900 mb-4">💡 优化建议</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 class="font-medium text-green-800 mb-2">请求优化</h5>
                        <ul class="text-green-700 text-sm space-y-1">
                          <li>• 使用批量API减少请求次数</li>
                          <li>• 实现客户端缓存机制</li>
                          <li>• 合理设置请求间隔</li>
                          <li>• 监控限流响应头</li>
                        </ul>
                      </div>
                      <div>
                        <h5 class="font-medium text-green-800 mb-2">错误处理</h5>
                        <ul class="text-green-700 text-sm space-y-1">
                          <li>• 实现指数退避重试</li>
                          <li>• 解析Retry-After头</li>
                          <li>• 记录限流日志</li>
                          <li>• 设置降级策略</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 错误处理 -->
              <section id="errors" data-section="errors" class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">错误处理</h2>
                <div class="prose max-w-none">
                  <p class="text-gray-600 mb-4">
                    我们的API使用标准的HTTP状态码来表示请求的结果。所有错误响应都包含详细的错误信息。
                  </p>
                  
                  <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-gray-900 mb-2">常见状态码</h4>
                      <ul class="text-sm text-gray-600 space-y-1">
                        <li><code class="bg-gray-100 px-2 py-1 rounded">200</code> - 请求成功</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">400</code> - 请求参数错误</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">401</code> - 未授权</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">403</code> - 禁止访问</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">404</code> - 资源不存在</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">429</code> - 请求过于频繁</li>
                        <li><code class="bg-gray-100 px-2 py-1 rounded">500</code> - 服务器内部错误</li>
                      </ul>
                    </div>
                    
                    <div class="bg-gray-900 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-white mb-2">错误响应示例</h4>
                      <pre class="text-green-400 text-sm"><code>{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": "email字段是必需的"
  }
}</code></pre>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 用户API -->
              <section id="users" data-section="users" class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">用户API</h2>
                <div class="prose max-w-none">
                  <p class="text-gray-600 mb-6">
                    用户API提供了用户信息的增删改查功能。
                  </p>
                  
                  <div class="space-y-6">
                    <div class="border border-gray-200 rounded-lg p-6">
                      <div class="flex items-center space-x-3 mb-4">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 text-xs font-medium rounded">GET</span>
                        <code class="text-sm font-mono">/api/v1/users/{id}</code>
                      </div>
                      <h4 class="text-lg font-medium text-gray-900 mb-2">获取用户信息</h4>
                      <p class="text-gray-600 mb-4">根据用户ID获取用户的详细信息。</p>
                      
                      <div class="bg-gray-900 rounded-lg p-4">
                        <pre class="text-green-400 text-sm"><code>curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.example.com/v1/users/123</code></pre>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 快速开始指南 -->
              <section class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-8 mt-12">
                <div class="text-center">
                  <CommandLineIcon class="mx-auto h-12 w-12 text-primary-600 mb-4" />
                  <h3 class="text-xl font-bold text-gray-900 mb-2">准备开始了吗？</h3>
                  <p class="text-gray-600 mb-6">
                    获取您的API密钥，开始构建您的应用程序
                  </p>
                  <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <router-link
                      to="/auth/register"
                      class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                    >
                      免费注册
                      <ArrowRightIcon class="ml-2 h-5 w-5" />
                    </router-link>
                    <router-link
                      to="/apis"
                      class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      浏览API
                    </router-link>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 开发文档页面样式 */
.prose code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}

.prose pre {
  @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;
}
</style>
