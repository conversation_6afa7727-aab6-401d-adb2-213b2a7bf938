<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useToast } from 'vue-toastification'
import { 
  PlayIcon,
  ClipboardDocumentIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import { apisApi } from '@/api/apis'
import type { ApiEndpoint, ApiTestRequest, ApiTestResponse } from '@/types'

interface Props {
  api: ApiEndpoint
}

const props = defineProps<Props>()
const toast = useToast()

const testing = ref(false)
const testResult = ref<ApiTestResponse | null>(null)

// 测试请求表单
const testRequest = reactive<ApiTestRequest>({
  method: props.api.method,
  url: props.api.endpoint,
  headers: {},
  query_params: {},
  body: '',
  auth_token: ''
})

// 动态头部和查询参数
const headers = ref<Array<{ key: string; value: string }>>([
  { key: 'Content-Type', value: 'application/json' }
])

const queryParams = ref<Array<{ key: string; value: string }>>([])

// 计算属性
const hasBody = computed(() => ['POST', 'PUT', 'PATCH'].includes(testRequest.method))
const fullUrl = computed(() => {
  const baseUrl = testRequest.url
  const params = queryParams.value
    .filter(p => p.key && p.value)
    .map(p => `${encodeURIComponent(p.key)}=${encodeURIComponent(p.value)}`)
    .join('&')
  
  return params ? `${baseUrl}?${params}` : baseUrl
})

// 添加头部
const addHeader = () => {
  headers.value.push({ key: '', value: '' })
}

// 删除头部
const removeHeader = (index: number) => {
  headers.value.splice(index, 1)
}

// 添加查询参数
const addQueryParam = () => {
  queryParams.value.push({ key: '', value: '' })
}

// 删除查询参数
const removeQueryParam = (index: number) => {
  queryParams.value.splice(index, 1)
}

// 执行测试
const executeTest = async () => {
  try {
    testing.value = true
    
    // 构建请求头
    const requestHeaders: Record<string, string> = {}
    headers.value.forEach(header => {
      if (header.key && header.value) {
        requestHeaders[header.key] = header.value
      }
    })
    
    // 构建查询参数
    const requestQueryParams: Record<string, string> = {}
    queryParams.value.forEach(param => {
      if (param.key && param.value) {
        requestQueryParams[param.key] = param.value
      }
    })
    
    // 构建请求体
    let requestBody: any = undefined
    if (hasBody.value && testRequest.body) {
      try {
        requestBody = JSON.parse(testRequest.body)
      } catch {
        requestBody = testRequest.body
      }
    }
    
    const request: ApiTestRequest = {
      method: testRequest.method,
      url: testRequest.url,
      headers: requestHeaders,
      query_params: requestQueryParams,
      body: requestBody,
      auth_token: testRequest.auth_token || undefined
    }
    
    const response = await apisApi.testApi(request)
    
    if (response.success) {
      testResult.value = response.data || null
      toast.success('测试完成')
    } else {
      toast.error('测试失败')
    }
  } catch (error: any) {
    console.error('API test failed:', error)
    toast.error(error.message || '测试失败')
  } finally {
    testing.value = false
  }
}

// 复制结果
const copyResult = () => {
  if (testResult.value) {
    const result = JSON.stringify(testResult.value, null, 2)
    navigator.clipboard.writeText(result)
    toast.success('结果已复制到剪贴板')
  }
}

// 格式化JSON
const formatJson = (json: any): string => {
  try {
    return JSON.stringify(json, null, 2)
  } catch {
    return String(json)
  }
}

// 获取状态颜色
const getStatusColor = (status: number): string => {
  if (status >= 200 && status < 300) return 'text-green-600'
  if (status >= 300 && status < 400) return 'text-yellow-600'
  if (status >= 400 && status < 500) return 'text-orange-600'
  return 'text-red-600'
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">API测试工具</h3>
      
      <!-- 请求配置 -->
      <div class="space-y-6">
        <!-- URL和方法 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="form-label">HTTP方法</label>
            <select v-model="testRequest.method" class="form-input">
              <option value="GET">GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="DELETE">DELETE</option>
              <option value="PATCH">PATCH</option>
            </select>
          </div>
          <div class="md:col-span-3">
            <label class="form-label">请求URL</label>
            <input
              v-model="testRequest.url"
              type="text"
              class="form-input"
              placeholder="输入API端点URL"
            />
          </div>
        </div>

        <!-- 认证Token -->
        <div>
          <label class="form-label">认证Token (可选)</label>
          <input
            v-model="testRequest.auth_token"
            type="text"
            class="form-input"
            placeholder="Bearer token或API key"
          />
        </div>

        <!-- 查询参数 -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <label class="form-label">查询参数</label>
            <button
              @click="addQueryParam"
              class="btn-outline btn-sm flex items-center gap-1"
            >
              <PlusIcon class="w-4 h-4" />
              添加
            </button>
          </div>
          <div class="space-y-2">
            <div
              v-for="(param, index) in queryParams"
              :key="index"
              class="flex gap-2"
            >
              <input
                v-model="param.key"
                type="text"
                placeholder="参数名"
                class="form-input flex-1"
              />
              <input
                v-model="param.value"
                type="text"
                placeholder="参数值"
                class="form-input flex-1"
              />
              <button
                @click="removeQueryParam(index)"
                class="btn-outline btn-sm text-red-600 hover:text-red-700"
              >
                <XMarkIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- 请求头 -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <label class="form-label">请求头</label>
            <button
              @click="addHeader"
              class="btn-outline btn-sm flex items-center gap-1"
            >
              <PlusIcon class="w-4 h-4" />
              添加
            </button>
          </div>
          <div class="space-y-2">
            <div
              v-for="(header, index) in headers"
              :key="index"
              class="flex gap-2"
            >
              <input
                v-model="header.key"
                type="text"
                placeholder="头部名称"
                class="form-input flex-1"
              />
              <input
                v-model="header.value"
                type="text"
                placeholder="头部值"
                class="form-input flex-1"
              />
              <button
                @click="removeHeader(index)"
                class="btn-outline btn-sm text-red-600 hover:text-red-700"
              >
                <XMarkIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- 请求体 -->
        <div v-if="hasBody">
          <label class="form-label">请求体 (JSON)</label>
          <textarea
            v-model="testRequest.body"
            rows="6"
            class="form-input font-mono text-sm"
            placeholder='{"key": "value"}'
          ></textarea>
        </div>

        <!-- 测试按钮 -->
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-600">
            <strong>完整URL:</strong> {{ fullUrl }}
          </div>
          <button
            @click="executeTest"
            :disabled="testing"
            class="btn-primary flex items-center gap-2"
          >
            <PlayIcon class="w-4 h-4" />
            {{ testing ? '测试中...' : '发送请求' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResult" class="border-t border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">测试结果</h4>
        <button
          @click="copyResult"
          class="btn-outline btn-sm flex items-center gap-1"
        >
          <ClipboardDocumentIcon class="w-4 h-4" />
          复制
        </button>
      </div>

      <!-- 响应状态 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="text-sm font-medium text-gray-700">状态码</label>
          <div :class="getStatusColor(testResult.status)" class="text-lg font-semibold">
            {{ testResult.status }} {{ testResult.status_text }}
          </div>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">响应时间</label>
          <div class="text-lg font-semibold text-gray-900">
            {{ testResult.response_time }}ms
          </div>
        </div>
        <div v-if="testResult.error">
          <label class="text-sm font-medium text-gray-700">错误信息</label>
          <div class="text-lg font-semibold text-red-600">
            {{ testResult.error }}
          </div>
        </div>
      </div>

      <!-- 响应头 -->
      <div class="mb-4">
        <label class="text-sm font-medium text-gray-700 mb-2 block">响应头</label>
        <pre class="bg-gray-50 p-3 rounded text-sm overflow-x-auto">{{ formatJson(testResult.headers) }}</pre>
      </div>

      <!-- 响应体 -->
      <div>
        <label class="text-sm font-medium text-gray-700 mb-2 block">响应体</label>
        <pre class="bg-gray-50 p-3 rounded text-sm overflow-x-auto max-h-96">{{ formatJson(testResult.data) }}</pre>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-sm {
  @apply px-2 py-1 text-xs;
}
</style>
