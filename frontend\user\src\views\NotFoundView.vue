<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-primary-600">404</h1>
        <h2 class="mt-4 text-3xl font-bold tracking-tight text-gray-900">
          页面未找到
        </h2>
        <p class="mt-2 text-lg text-gray-600">
          抱歉，您访问的页面不存在。
        </p>
      </div>
      
      <div class="space-y-4">
        <RouterLink
          to="/"
          class="btn-primary inline-block"
        >
          返回首页
        </RouterLink>
        <div>
          <RouterLink
            to="/apis"
            class="text-primary-600 hover:text-primary-500 font-medium"
          >
            浏览API列表
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 404 page specific styles */
</style>
