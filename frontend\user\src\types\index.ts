// Copyright (c) 2025 Aoki. All rights reserved.

// 用户相关类型
export interface User {
  id: number
  name: string
  email: string
  avatar?: string
  avatar_url?: string
  email_verified_at?: string
  created_at: string
  updated_at: string
  phone?: string
  company?: string
  position?: string
  website?: string
  bio?: string
}

// 认证相关类型
export interface AuthResponse {
  user: User
  token: string
  expires_at: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  password_confirmation: string
}

// API相关类型
export interface ApiEndpoint {
  id: number
  category_id: number
  name: string
  description: string
  method: string
  endpoint: string
  status?: string
  version: string
  lastUpdated?: string
  is_public: boolean
  is_active: boolean
  requires_auth: boolean
  total_calls?: number
  rate_limit?: number
  parameters?: any[]
  responses?: any[]
  examples?: any[]
  created_at: string
  updated_at: string
  category: {
    name: string
  }
}

export interface ApiCategory {
  id: number
  name: string
  description?: string
  icon?: string
  sort_order?: number
  is_active?: boolean
  created_at?: string
  updated_at?: string
  count?: number
}

export interface ApiSearchParams {
  keyword: string
  category_id?: number
  method?: string
  status?: string
  sort_by: string
  sort_order: string
  per_page: number
  page?: number
  is_public?: boolean
  requires_auth?: boolean
}

// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

// 分页类型
export interface PaginatedResponse<T> {
  data: T[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  from?: number
  to?: number
}

// API统计类型
export interface ApiStats {
  total_apis: number
  public_apis: number
  categories_count: number
  average_rating: number
  total_favorites: number
}

// API测试类型
export interface ApiTestRequest {
  method: string
  url: string
  headers?: Record<string, string>
  body?: any
  query_params?: Record<string, any>
  auth_token?: string
}

export interface ApiTestResponse {
  status: number
  status_text: string
  headers: Record<string, string>
  data: any
  response_time: number
  error?: string
}

// API收藏类型
export interface ApiFavorite {
  id: number
  user_id: number
  api_id: number
  created_at: string
  updated_at: string
}

// API评分类型
export interface ApiRating {
  id: number
  user_id: number
  api_id: number
  rating: number
  comment?: string
  created_at: string
  updated_at: string
  user?: {
    id: number
    name: string
    avatar?: string
    avatar_url?: string
  }
}
