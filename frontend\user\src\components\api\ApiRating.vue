<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useToast } from 'vue-toastification'
import { 
  StarIcon as StarOutlineIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/vue/24/solid'
import { apisApi } from '@/api/apis'
import { useAuthStore } from '@/stores/auth'
import type { ApiRating } from '@/types'

interface Props {
  apiId: number
}

const props = defineProps<Props>()
const toast = useToast()
const authStore = useAuthStore()

const loading = ref(false)
const ratings = ref<ApiRating[]>([])
const userRating = ref<ApiRating | null>(null)
const showRatingForm = ref(false)

// 评价表单
const ratingForm = reactive({
  rating: 0,
  comment: ''
})

// 计算属性
const averageRating = computed(() => {
  if (ratings.value.length === 0) return 0
  const sum = ratings.value.reduce((acc, rating) => acc + rating.rating, 0)
  return Math.round((sum / ratings.value.length) * 10) / 10
})

const totalRatings = computed(() => ratings.value.length)

const ratingDistribution = computed(() => {
  const distribution = [0, 0, 0, 0, 0] // 1-5星的分布
  ratings.value.forEach(rating => {
    if (rating.rating >= 1 && rating.rating <= 5) {
      distribution[rating.rating - 1]++
    }
  })
  return distribution.map(count => ({
    count,
    percentage: totalRatings.value > 0 ? (count / totalRatings.value) * 100 : 0
  }))
})

const canRate = computed(() => authStore.isAuthenticated)
const hasUserRating = computed(() => userRating.value !== null)

// 加载评价数据
const loadRatings = async () => {
  try {
    loading.value = true
    const response = await apisApi.getRatings(props.apiId)
    if (response.success) {
      ratings.value = response.data
    }
  } catch (error) {
    console.error('Failed to load ratings:', error)
  } finally {
    loading.value = false
  }
}

// 加载用户评价
const loadUserRating = async () => {
  if (!authStore.isAuthenticated) return
  
  try {
    const response = await apisApi.getUserRating(props.apiId)
    if (response.success) {
      userRating.value = response.data
      if (userRating.value) {
        ratingForm.rating = userRating.value.rating
        ratingForm.comment = userRating.value.comment || ''
      }
    }
  } catch (error) {
    console.error('Failed to load user rating:', error)
  }
}

// 提交评价
const submitRating = async () => {
  if (!canRate.value) {
    toast.warning('请先登录')
    return
  }
  
  if (ratingForm.rating === 0) {
    toast.warning('请选择评分')
    return
  }
  
  try {
    loading.value = true
    
    if (hasUserRating.value) {
      // 更新评价
      await apisApi.updateRating(props.apiId, ratingForm.rating, ratingForm.comment)
      toast.success('评价更新成功')
    } else {
      // 添加评价
      await apisApi.addRating(props.apiId, ratingForm.rating, ratingForm.comment)
      toast.success('评价提交成功')
    }
    
    showRatingForm.value = false
    await loadRatings()
    await loadUserRating()
  } catch (error: any) {
    console.error('Failed to submit rating:', error)
    toast.error(error.message || '评价提交失败')
  } finally {
    loading.value = false
  }
}

// 删除评价
const deleteRating = async () => {
  if (!hasUserRating.value) return
  
  try {
    loading.value = true
    await apisApi.deleteRating(props.apiId)
    toast.success('评价删除成功')
    
    userRating.value = null
    ratingForm.rating = 0
    ratingForm.comment = ''
    showRatingForm.value = false
    
    await loadRatings()
  } catch (error: any) {
    console.error('Failed to delete rating:', error)
    toast.error(error.message || '评价删除失败')
  } finally {
    loading.value = false
  }
}

// 设置评分
const setRating = (rating: number) => {
  ratingForm.rating = rating
}

// 取消评价
const cancelRating = () => {
  showRatingForm.value = false
  if (hasUserRating.value) {
    ratingForm.rating = userRating.value!.rating
    ratingForm.comment = userRating.value!.comment || ''
  } else {
    ratingForm.rating = 0
    ratingForm.comment = ''
  }
}

// 格式化时间
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadRatings()
  loadUserRating()
})
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6">用户评价</h3>
      
      <!-- 评价概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- 平均评分 -->
        <div class="text-center">
          <div class="text-4xl font-bold text-gray-900 mb-2">{{ averageRating }}</div>
          <div class="flex justify-center mb-2">
            <StarSolidIcon
              v-for="i in 5"
              :key="i"
              :class="[
                'w-5 h-5',
                i <= Math.round(averageRating) ? 'text-yellow-400' : 'text-gray-300'
              ]"
            />
          </div>
          <div class="text-sm text-gray-600">基于 {{ totalRatings }} 个评价</div>
        </div>
        
        <!-- 评分分布 -->
        <div class="space-y-2">
          <div
            v-for="(dist, index) in ratingDistribution.slice().reverse()"
            :key="5 - index"
            class="flex items-center text-sm"
          >
            <span class="w-8 text-gray-600">{{ 5 - index }}星</span>
            <div class="flex-1 mx-3 bg-gray-200 rounded-full h-2">
              <div
                class="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${dist.percentage}%` }"
              ></div>
            </div>
            <span class="w-8 text-gray-600 text-right">{{ dist.count }}</span>
          </div>
        </div>
      </div>

      <!-- 用户评价操作 -->
      <div v-if="canRate" class="mb-6 p-4 bg-gray-50 rounded-lg">
        <div v-if="!showRatingForm" class="flex items-center justify-between">
          <div v-if="hasUserRating" class="flex items-center space-x-4">
            <div class="flex">
              <StarSolidIcon
                v-for="i in 5"
                :key="i"
                :class="[
                  'w-5 h-5',
                  i <= userRating!.rating ? 'text-yellow-400' : 'text-gray-300'
                ]"
              />
            </div>
            <span class="text-sm text-gray-600">您的评价</span>
          </div>
          <div v-else>
            <span class="text-sm text-gray-600">您还没有评价过这个API</span>
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="showRatingForm = true"
              class="btn-outline btn-sm flex items-center gap-1"
            >
              <PencilIcon class="w-4 h-4" />
              {{ hasUserRating ? '修改评价' : '写评价' }}
            </button>
            <button
              v-if="hasUserRating"
              @click="deleteRating"
              class="btn-outline btn-sm text-red-600 hover:text-red-700 flex items-center gap-1"
            >
              <TrashIcon class="w-4 h-4" />
              删除
            </button>
          </div>
        </div>

        <!-- 评价表单 -->
        <div v-else class="space-y-4">
          <div>
            <label class="form-label">评分</label>
            <div class="flex space-x-1">
              <button
                v-for="i in 5"
                :key="i"
                @click="setRating(i)"
                class="p-1 hover:scale-110 transition-transform"
              >
                <StarSolidIcon
                  :class="[
                    'w-6 h-6',
                    i <= ratingForm.rating ? 'text-yellow-400' : 'text-gray-300'
                  ]"
                />
              </button>
            </div>
          </div>
          
          <div>
            <label class="form-label">评价内容 (可选)</label>
            <textarea
              v-model="ratingForm.comment"
              rows="3"
              class="form-input"
              placeholder="分享您使用这个API的体验..."
            ></textarea>
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="submitRating"
              :disabled="loading || ratingForm.rating === 0"
              class="btn-primary disabled:opacity-50"
            >
              {{ loading ? '提交中...' : '提交评价' }}
            </button>
            <button
              @click="cancelRating"
              class="btn-outline"
            >
              取消
            </button>
          </div>
        </div>
      </div>

      <!-- 评价列表 -->
      <div v-if="totalRatings > 0" class="space-y-4">
        <h4 class="font-medium text-gray-900">所有评价</h4>
        <div
          v-for="rating in ratings"
          :key="rating.id"
          class="border-b border-gray-200 pb-4 last:border-b-0"
        >
          <div class="flex items-start justify-between mb-2">
            <div class="flex items-center space-x-3">
              <img
                :src="rating.user?.avatar_url || 'http://localhost/images/default-avatar.svg'"
                :alt="rating.user?.name"
                class="w-8 h-8 rounded-full"
              />
              <div>
                <div class="font-medium text-gray-900">{{ rating.user?.name || '匿名用户' }}</div>
                <div class="flex items-center space-x-2">
                  <div class="flex">
                    <StarSolidIcon
                      v-for="i in 5"
                      :key="i"
                      :class="[
                        'w-4 h-4',
                        i <= rating.rating ? 'text-yellow-400' : 'text-gray-300'
                      ]"
                    />
                  </div>
                  <span class="text-sm text-gray-500">{{ formatDate(rating.created_at) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <p v-if="rating.comment" class="text-gray-700 text-sm ml-11">
            {{ rating.comment }}
          </p>
        </div>
      </div>
      
      <div v-else-if="!loading" class="text-center py-8 text-gray-500">
        暂无评价，成为第一个评价者吧！
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-sm {
  @apply px-2 py-1 text-xs;
}
</style>
