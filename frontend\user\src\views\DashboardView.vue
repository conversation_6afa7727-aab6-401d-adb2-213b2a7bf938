<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import {
  ChartBarIcon,
  KeyIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const stats = ref({
  apiCalls: 12543,
  apiKeys: 3,
  successRate: 98.5,
  avgResponseTime: 245,
  todayCalls: 1250,
  monthlyQuota: 10000
})

const loading = ref(false)

const quotaUsagePercentage = computed(() => {
  return Math.round((stats.value.todayCalls / stats.value.monthlyQuota) * 100)
})
</script>

<template>
  <div class="h-full p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            欢迎回来，{{ authStore.user?.name || '用户' }}
          </h1>
          <p class="mt-1 text-sm text-gray-500">这是您的API使用概览和快速操作中心</p>
        </div>
        <div class="flex space-x-3">
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      <p class="mt-2 text-sm text-gray-500">加载中...</p>
    </div>

    <div v-else class="space-y-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ChartBarIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总调用次数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.apiCalls.toLocaleString() }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-green-600 font-medium flex items-center">
                <ArrowUpIcon class="h-4 w-4 mr-1" />
                12%
              </span>
              <span class="text-gray-500 ml-2">较上月</span>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <KeyIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">API密钥</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.apiKeys }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <router-link to="/api-keys" class="text-primary-600 hover:text-primary-700">
                管理密钥 →
              </router-link>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <CheckCircleIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">成功率</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.successRate }}%</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-green-600 font-medium flex items-center">
                <ArrowUpIcon class="h-4 w-4 mr-1" />
                2.1%
              </span>
              <span class="text-gray-500 ml-2">较上月</span>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <ClockIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.avgResponseTime }}ms</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-green-600 font-medium flex items-center">
                <ArrowDownIcon class="h-4 w-4 mr-1" />
                5ms
              </span>
              <span class="text-gray-500 ml-2">较上月</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用配额 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">本月使用配额</h3>
        </div>
        <div class="px-6 py-6">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">
              已使用 {{ stats.todayCalls.toLocaleString() }} / {{ stats.monthlyQuota.toLocaleString() }} 次调用
            </span>
            <span class="text-sm text-gray-500">{{ quotaUsagePercentage }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              :class="[
                'h-2 rounded-full transition-all duration-300',
                quotaUsagePercentage > 80 ? 'bg-red-500' :
                quotaUsagePercentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
              ]"
              :style="{ width: `${Math.min(quotaUsagePercentage, 100)}%` }"
            ></div>
          </div>
          <p class="mt-2 text-sm text-gray-500">
            配额将在每月1日重置。如需增加配额，请联系客服。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 控制台页面样式 */
</style>