// Copyright (c) 2025 Aoki. All rights reserved.

import request from '@/utils/request'
import type {
  ApiCategory,
  ApiEndpoint,
  PaginatedResponse,
  ApiSearchParams,
  ApiStats,
  ApiFavorite,
  ApiRating,
  ApiTestRequest,
  ApiTestResponse
} from '@/types'

import { mockApis, mockStats, mockFavorites, mockRatings } from '@/data/mockData'

// 禁用模拟数据，使用真实API
const isDev = false

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const apisApi = {
  // 获取API分类列表
  getCategories: async (): Promise<{ success: boolean; data: ApiCategory[]; message: string }> => {
    return request.get('/public-categories')
  },

  // 获取当前用户的API列表
  getUserApis: async (params?: ApiSearchParams): Promise<{ success: boolean; data: PaginatedResponse<ApiEndpoint>; message: string }> => {
    return request.get('/user/apis', { params })
  },

  // 获取API列表
  getApis: async (params?: ApiSearchParams): Promise<{ success: boolean; data: PaginatedResponse<ApiEndpoint>; message: string }> => {
    if (isDev) {
      await delay(800)

      let filteredApis = [...mockApis]

      // 应用筛选条件
      if (params?.keyword) {
        const keyword = params.keyword.toLowerCase()
        filteredApis = filteredApis.filter(api =>
          api.name.toLowerCase().includes(keyword) ||
          api.description.toLowerCase().includes(keyword) ||
          api.endpoint.toLowerCase().includes(keyword)
        )
      }

      if (params?.category_id) {
        filteredApis = filteredApis.filter(api => api.category_id === params.category_id)
      }

      if (params?.method) {
        filteredApis = filteredApis.filter(api => api.method === params.method)
      }

      if (params?.is_public !== undefined) {
        filteredApis = filteredApis.filter(api => api.is_public === params.is_public)
      }

      if (params?.requires_auth !== undefined) {
        filteredApis = filteredApis.filter(api => api.requires_auth === params.requires_auth)
      }

      // 应用排序
      if (params?.sort_by) {
        filteredApis.sort((a, b) => {
          let aValue: any, bValue: any

          switch (params.sort_by) {
            case 'name':
              aValue = a.name
              bValue = b.name
              break
            case 'created_at':
              aValue = new Date(a.created_at)
              bValue = new Date(b.created_at)
              break
            case 'rating':
              aValue = 4.2 // 模拟评分
              bValue = 4.2
              break
            case 'popularity':
              aValue = Math.random()
              bValue = Math.random()
              break
            default:
              return 0
          }

          if (params.sort_order === 'desc') {
            return aValue < bValue ? 1 : -1
          }
          return aValue > bValue ? 1 : -1
        })
      }

      // 分页
      const page = params?.page || 1
      const perPage = params?.per_page || 12
      const total = filteredApis.length
      const from = (page - 1) * perPage
      const to = Math.min(from + perPage, total)
      const data = filteredApis.slice(from, to)

      return {
        success: true,
        data: {
          data,
          current_page: page,
          last_page: Math.ceil(total / perPage),
          per_page: perPage,
          total,
          from: from + 1,
          to
        },
        message: 'Success'
      } as any
    }
    return request.get('/public-apis', { params })
  },

  // 获取API详情
  getApiDetail: async (id: number): Promise<{ success: boolean; data: ApiEndpoint; message: string }> => {
    if (isDev) {
      await delay(500)
      const api = mockApis.find(api => api.id === id)
      if (api) {
        return {
          success: true,
          data: api,
          message: 'Success'
        }
      } else {
        throw new Error('API not found')
      }
    }
    return request.get(`/apis/${id}`)
  },

  // 搜索API
  searchApis: (params: ApiSearchParams): Promise<{ success: boolean; data: PaginatedResponse<ApiEndpoint>; message: string }> => {
    return apisApi.getApis(params)
  },

  // 获取热门API
  getPopularApis: async (limit = 10): Promise<{ success: boolean; data: ApiEndpoint[]; message: string }> => {
    if (isDev) {
      await delay(600)
      return {
        success: true,
        data: mockApis.slice(0, limit),
        message: 'Success'
      }
    }
    return request.get('/apis/popular', { params: { limit } })
  },

  // 获取最新API
  getLatestApis: async (limit = 10): Promise<{ success: boolean; data: ApiEndpoint[]; message: string }> => {
    if (isDev) {
      await delay(600)
      const sortedApis = [...mockApis].sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      return {
        success: true,
        data: sortedApis.slice(0, limit),
        message: 'Success'
      }
    }
    return request.get('/apis/latest', { params: { limit } })
  },

  // 获取API统计信息
  getApiStats: async (): Promise<{ success: boolean; data: ApiStats; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        data: mockStats,
        message: 'Success'
      }
    }
    return request.get('/apis/stats')
  },

  // 测试API
  testApi: async (data: ApiTestRequest): Promise<{ success: boolean; data: ApiTestResponse; message: string }> => {
    if (isDev) {
      await delay(1000)
      // 模拟API测试响应
      return {
        success: true,
        data: {
          status: 200,
          status_text: 'OK',
          headers: {
            'content-type': 'application/json',
            'x-response-time': '123ms'
          },
          data: {
            success: true,
            message: '测试成功',
            data: { result: 'mock response' }
          },
          response_time: 123
        },
        message: 'Success'
      }
    }
    return request.post('/apis/test', data)
  },

  // 收藏相关
  getFavorites: async (): Promise<{ success: boolean; data: ApiFavorite[]; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        data: mockFavorites,
        message: 'Success'
      }
    }
    return request.get('/user/favorites')
  },

  addFavorite: async (apiId: number): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(300)
      return {
        success: true,
        message: '收藏成功'
      }
    }
    return request.post('/user/favorites', { api_id: apiId })
  },

  removeFavorite: async (apiId: number): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(300)
      return {
        success: true,
        message: '取消收藏成功'
      }
    }
    return request.delete(`/user/favorites/${apiId}`)
  },

  // 评价相关
  getRatings: async (apiId: number): Promise<{ success: boolean; data: ApiRating[]; message: string }> => {
    if (isDev) {
      await delay(500)
      return {
        success: true,
        data: mockRatings.filter(rating => rating.api_id === apiId),
        message: 'Success'
      }
    }
    return request.get(`/apis/${apiId}/ratings`)
  },

  addRating: async (apiId: number, rating: number, comment?: string): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        message: '评价提交成功'
      }
    }
    return request.post(`/apis/${apiId}/ratings`, { rating, comment })
  },

  updateRating: async (apiId: number, rating: number, comment?: string): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(400)
      return {
        success: true,
        message: '评价更新成功'
      }
    }
    return request.put(`/apis/${apiId}/ratings`, { rating, comment })
  },

  deleteRating: async (apiId: number): Promise<{ success: boolean; message: string }> => {
    if (isDev) {
      await delay(300)
      return {
        success: true,
        message: '评价删除成功'
      }
    }
    return request.delete(`/apis/${apiId}/ratings`)
  },

  // 获取用户对特定API的评价
  getUserRating: async (apiId: number): Promise<{ success: boolean; data: ApiRating | null; message: string }> => {
    if (isDev) {
      await delay(300)
      const userRating = mockRatings.find(rating => rating.api_id === apiId && rating.user_id === 1)
      return {
        success: true,
        data: userRating || null,
        message: 'Success'
      }
    }
    return request.get(`/apis/${apiId}/ratings/user`)
  }
}
