<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useSystemStore } from '@/stores/system'
import DashboardSidebar from './DashboardSidebar.vue'
import {
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()
const systemStore = useSystemStore()
const sidebarOpen = ref(false)
const userMenuOpen = ref(false)

// 初始化系统设置
onMounted(() => {
  systemStore.initSettings()
})

// 用户下拉菜单导航
const userNavigation = computed(() => [
  { name: '控制台', href: '/dashboard', icon: UserIcon },
  { name: '个人资料', href: '/profile', icon: Cog6ToothIcon },
  { name: 'API密钥', href: '/api-keys', icon: Cog6ToothIcon },
])

const handleLogout = () => {
  authStore.logout()
  userMenuOpen.value = false
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 桌面端布局 -->
    <div class="hidden md:block">
      <!-- 顶部导航栏 - 横跨整个页面宽度 -->
      <div class="fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <router-link to="/" class="flex items-center space-x-2">
                  <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">API</span>
                  </div>
                  <span class="text-xl font-bold text-gray-900">{{ systemStore.systemName }}</span>
                </router-link>
              </div>
            </div>

            <!-- 用户菜单 -->
            <div class="flex items-center">
              <!-- 已登录状态的用户菜单 -->
              <div v-if="authStore.isAuthenticated" class="relative">
                <button
                  @click="userMenuOpen = !userMenuOpen"
                  class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  <img
                    class="h-8 w-8 rounded-full"
                    :src="authStore.user?.avatar_url || 'http://localhost/images/default-avatar.svg'"
                    :alt="authStore.user?.name"
                  />
                  <span class="hidden md:block text-gray-700 font-medium">{{ authStore.user?.name }}</span>
                </button>

                <!-- 用户下拉菜单 -->
                <div
                  v-show="userMenuOpen"
                  @click.self="userMenuOpen = false"
                  class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <router-link
                    v-for="item in userNavigation"
                    :key="item.name"
                    :to="item.href"
                    @click="userMenuOpen = false"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <component :is="item.icon" class="mr-3 h-4 w-4" />
                    {{ item.name }}
                  </router-link>
                  <button
                    @click="handleLogout"
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                    退出登录
                  </button>
                </div>
              </div>

              <!-- 未登录状态 -->
              <div v-else class="flex items-center space-x-4">
                <router-link
                  to="/auth/login"
                  class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >
                  登录
                </router-link>
                <router-link
                  to="/auth/register"
                  class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  注册
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 - 包含侧边栏和内容 -->
      <div class="flex pt-16 h-screen">
        <!-- 左侧固定侧边栏 -->
        <div class="w-64 bg-white shadow-sm border-r border-gray-200 flex-shrink-0">
          <DashboardSidebar />
        </div>

        <!-- 右侧主内容区域 -->
        <div class="flex-1 overflow-auto">
          <main class="bg-gray-50 min-h-full">
            <slot />
          </main>
        </div>
      </div>
    </div>

    <!-- 移动端布局 -->
    <div class="md:hidden">
      <!-- 移动端顶部导航栏 - 横跨整个页面宽度 -->
      <div class="fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button
                @click="sidebarOpen = true"
                class="mr-4 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
              >
                <Bars3Icon class="h-6 w-6" />
              </button>
              <div class="flex-shrink-0">
                <router-link to="/" class="flex items-center space-x-2">
                  <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">API</span>
                  </div>
                  <span class="text-xl font-bold text-gray-900">API平台</span>
                </router-link>
              </div>
            </div>

            <!-- 用户菜单 -->
            <div class="flex items-center">
              <!-- 已登录状态的用户菜单 -->
              <div v-if="authStore.isAuthenticated" class="relative">
                <button
                  @click="userMenuOpen = !userMenuOpen"
                  class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  <img
                    class="h-8 w-8 rounded-full"
                    :src="authStore.user?.avatar_url || 'http://localhost/images/default-avatar.svg'"
                    :alt="authStore.user?.name"
                  />
                  <span class="hidden sm:block text-gray-700 font-medium">{{ authStore.user?.name }}</span>
                </button>

                <!-- 用户下拉菜单 -->
                <div
                  v-show="userMenuOpen"
                  @click.self="userMenuOpen = false"
                  class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <router-link
                    v-for="item in userNavigation"
                    :key="item.name"
                    :to="item.href"
                    @click="userMenuOpen = false"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <component :is="item.icon" class="mr-3 h-4 w-4" />
                    {{ item.name }}
                  </router-link>
                  <button
                    @click="handleLogout"
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                    退出登录
                  </button>
                </div>
              </div>

              <!-- 未登录状态 -->
              <div v-else class="flex items-center space-x-2">
                <router-link
                  to="/auth/login"
                  class="text-gray-600 hover:text-gray-900 px-2 py-1 text-sm font-medium"
                >
                  登录
                </router-link>
                <router-link
                  to="/auth/register"
                  class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded-md text-sm font-medium"
                >
                  注册
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端主内容 -->
      <main class="pt-16 bg-gray-50 min-h-screen">
        <slot />
      </main>

      <!-- 移动端侧边栏覆盖层 -->
      <div v-if="sidebarOpen" class="fixed inset-0 flex z-40">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
        <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div class="absolute top-0 right-0 -mr-12 pt-2">
            <button
              @click="sidebarOpen = false"
              class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            >
              <XMarkIcon class="h-6 w-6 text-white" />
            </button>
          </div>
          <DashboardSidebar />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 控制台布局样式 */
</style>
