<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useAuthStore } from '@/stores/auth'
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PaperAirplaneIcon,
  UserIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const toast = useToast()
const authStore = useAuthStore()

// 用户认证状态
const isAuthenticated = computed(() => authStore.isAuthenticated)

// 表单数据
const form = reactive<{ name: string; email: string; subject: string; category: string; message: string; priority: string }>({
  name: '',
  email: '',
  subject: '',
  category: '',
  message: '',
  priority: 'normal'
})

// 状态
const loading = ref(false)
const errors = ref<Record<string, string>>({})

// 联系分类
const categories = [
  { value: 'technical', label: '技术支持', icon: '🔧' },
  { value: 'billing', label: '账单问题', icon: '💳' },
  { value: 'api', label: 'API相关', icon: '🔌' },
  { value: 'partnership', label: '商务合作', icon: '🤝' },
  { value: 'feedback', label: '意见反馈', icon: '💡' },
  { value: 'other', label: '其他问题', icon: '❓' }
]

// 优先级选项
const priorities = [
  { value: 'low', label: '低优先级', color: 'text-green-600' },
  { value: 'normal', label: '普通', color: 'text-blue-600' },
  { value: 'high', label: '高优先级', color: 'text-orange-600' },
  { value: 'urgent', label: '紧急', color: 'text-red-600' }
]

// 常见问题
const faqs = [
  {
    question: '如何获取API密钥？',
    answer: '登录控制台，进入"API密钥管理"页面，点击"生成新密钥"即可。',
    link: '/api-keys'
  },
  {
    question: 'API调用限制是多少？',
    answer: '不同套餐有不同的限制，免费版每分钟100次，专业版1000次。',
    link: '/docs#rate-limiting'
  },
  {
    question: '如何升级套餐？',
    answer: '在控制台的"套餐管理"页面可以升级您的套餐。',
    link: '/dashboard'
  },
  {
    question: '支持哪些编程语言？',
    answer: '我们支持JavaScript、Python、PHP、Java、Go等主流编程语言。',
    link: '/docs#introduction'
  }
]

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string> = {}

  if (!form.name.trim()) {
    newErrors.name = '请输入您的姓名'
  }
  
  if (!form.email.trim()) {
    newErrors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = '请输入有效的邮箱地址'
  }
  
  if (!form.subject.trim()) {
    newErrors.subject = '请输入主题'
  }
  
  if (!form.category) {
    newErrors.category = '请选择问题分类'
  }
  
  if (!form.message.trim()) {
    newErrors.message = '请输入详细描述'
  } else if (form.message.trim().length < 10) {
    newErrors.message = '描述至少需要10个字符'
  }
  
  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/auth/login')
}

// 提交表单
const submitForm = async () => {
  // 检查用户是否已登录
  if (!isAuthenticated.value) {
    toast.warning('请先登录后再发送消息')
    return
  }

  if (!validateForm()) {
    toast.error('请检查表单中的错误')
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    toast.success('消息发送成功！我们会在24小时内回复您。')

    // 重置表单
    Object.assign(form, {
      name: '',
      email: '',
      subject: '',
      category: '',
      message: '',
      priority: 'normal'
    })
    errors.value = {}
  } catch (error) {
    console.error('Submit error:', error)
    toast.error('发送失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-white mb-4">联系我们</h1>
          <p class="text-xl text-blue-100 max-w-3xl mx-auto">
            我们随时为您提供专业的技术支持和服务。无论您遇到什么问题，我们都会尽快为您解决。
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <!-- 左侧：联系方式和信息 -->
        <div class="lg:col-span-1 space-y-8">
          <!-- 联系方式 -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">联系方式</h3>
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <EnvelopeIcon class="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h4 class="text-lg font-semibold text-gray-900">邮箱支持</h4>
                <p class="text-gray-600 mt-2 text-lg"><EMAIL></p>
                <p class="text-sm text-gray-500 mt-1">我们会在24小时内回复您的邮件</p>
              </div>
            </div>
          </div>

          <!-- 技术支持时间 -->
          <div class="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-xl p-6">
            <div class="flex items-center mb-4">
              <ClockIcon class="h-6 w-6 text-orange-600 mr-3" />
              <h3 class="text-lg font-semibold text-orange-900">技术支持时间</h3>
            </div>
            <div class="space-y-3 text-orange-800">
              <div class="flex justify-between">
                <span>工作日</span>
                <span class="font-medium">9:00 - 18:00</span>
              </div>
              <div class="flex justify-between">
                <span>周末</span>
                <span class="font-medium">10:00 - 16:00</span>
              </div>
              <div class="flex justify-between">
                <span>紧急支持</span>
                <span class="font-medium">24/7</span>
              </div>
            </div>
            <div class="mt-4 p-3 bg-orange-100 rounded-lg">
              <p class="text-sm text-orange-700">
                <ExclamationTriangleIcon class="h-4 w-4 inline mr-1" />
                紧急问题请标记为"紧急"优先级，我们会立即处理。
              </p>
            </div>
          </div>

          <!-- 常见问题快速入口 -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
              <QuestionMarkCircleIcon class="h-6 w-6 text-blue-600 mr-3" />
              <h3 class="text-lg font-semibold text-gray-900">常见问题</h3>
            </div>
            <div class="space-y-3">
              <div v-for="faq in faqs" :key="faq.question" class="border-l-4 border-blue-200 pl-4">
                <h4 class="font-medium text-gray-900 text-sm">{{ faq.question }}</h4>
                <p class="text-gray-600 text-sm mt-1">{{ faq.answer }}</p>
                <a :href="faq.link" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  查看详情 →
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：联系表单 -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div class="flex items-center mb-8">
              <ChatBubbleLeftRightIcon class="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h2 class="text-2xl font-bold text-gray-900">发送消息</h2>
                <p class="text-gray-600 mt-1">请详细描述您的问题，我们会尽快回复您</p>
              </div>
            </div>

            <form @submit.prevent="submitForm" class="space-y-6">
              <!-- 基本信息 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-semibold text-gray-900 mb-2">
                    姓名 <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    :class="[
                      'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                      errors.name 
                        ? 'border-red-300 focus:border-red-500 bg-red-50' 
                        : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                    ]"
                    placeholder="请输入您的姓名"
                  />
                  <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
                </div>

                <div>
                  <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">
                    邮箱地址 <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    :class="[
                      'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                      errors.email 
                        ? 'border-red-300 focus:border-red-500 bg-red-50' 
                        : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                    ]"
                    placeholder="请输入您的邮箱地址"
                  />
                  <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
                </div>
              </div>

              <!-- 主题 -->
              <div>
                <label for="subject" class="block text-sm font-semibold text-gray-900 mb-2">
                  主题 <span class="text-red-500">*</span>
                </label>
                <input
                  id="subject"
                  v-model="form.subject"
                  type="text"
                  :class="[
                    'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                    errors.subject 
                      ? 'border-red-300 focus:border-red-500 bg-red-50' 
                      : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                  ]"
                  placeholder="简要描述您的问题"
                />
                <p v-if="errors.subject" class="mt-1 text-sm text-red-600">{{ errors.subject }}</p>
              </div>

              <!-- 问题分类和优先级 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-gray-900 mb-3">
                    问题分类 <span class="text-red-500">*</span>
                  </label>
                  <div class="grid grid-cols-2 gap-3">
                    <div
                      v-for="category in categories"
                      :key="category.value"
                      @click="form.category = category.value"
                      :class="[
                        'relative cursor-pointer rounded-lg border-2 p-3 text-center transition-all duration-200',
                        form.category === category.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      ]"
                    >
                      <div class="text-lg mb-1">{{ category.icon }}</div>
                      <div class="text-xs font-medium">{{ category.label }}</div>
                      <CheckCircleIcon 
                        v-if="form.category === category.value"
                        class="absolute top-1 right-1 h-4 w-4 text-blue-600"
                      />
                    </div>
                  </div>
                  <p v-if="errors.category" class="mt-2 text-sm text-red-600">{{ errors.category }}</p>
                </div>

                <div>
                  <label for="priority" class="block text-sm font-semibold text-gray-900 mb-2">
                    优先级
                  </label>
                  <select
                    id="priority"
                    v-model="form.priority"
                    class="block w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500 hover:border-gray-300 focus:outline-none focus:ring-0 transition-colors duration-200"
                  >
                    <option v-for="priority in priorities" :key="priority.value" :value="priority.value">
                      {{ priority.label }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- 详细描述 -->
              <div>
                <label for="message" class="block text-sm font-semibold text-gray-900 mb-2">
                  详细描述 <span class="text-red-500">*</span>
                </label>
                <textarea
                  id="message"
                  v-model="form.message"
                  rows="6"
                  :class="[
                    'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0 resize-none',
                    errors.message 
                      ? 'border-red-300 focus:border-red-500 bg-red-50' 
                      : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                  ]"
                  placeholder="请详细描述您遇到的问题，包括错误信息、操作步骤等..."
                ></textarea>
                <p v-if="errors.message" class="mt-1 text-sm text-red-600">{{ errors.message }}</p>
                <p class="mt-1 text-sm text-gray-500">
                  字符数：{{ form.message.length }} / 最少10个字符
                </p>
              </div>

              <!-- 登录提示 -->
              <div v-if="!isAuthenticated" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                  <ExclamationTriangleIcon class="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                  <div class="flex-1">
                    <h4 class="text-sm font-medium text-yellow-900">需要登录</h4>
                    <p class="mt-1 text-sm text-yellow-700">
                      您需要登录后才能发送消息。登录后我们可以更好地为您提供支持服务。
                    </p>
                  </div>
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="flex justify-end space-x-3">
                <!-- 登录按钮（未登录时显示） -->
                <button
                  v-if="!isAuthenticated"
                  type="button"
                  @click="goToLogin"
                  class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-base font-medium text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                >
                  <UserIcon class="h-5 w-5 mr-2" />
                  登录账号
                </button>

                <!-- 发送消息按钮 -->
                <button
                  type="submit"
                  :disabled="loading || !isAuthenticated"
                  :class="[
                    'inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-base font-medium transition-all duration-200',
                    isAuthenticated
                      ? 'text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                      : 'text-gray-400 bg-gray-200 cursor-not-allowed',
                    loading ? 'opacity-50 cursor-not-allowed' : ''
                  ]"
                >
                  <span v-if="loading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    发送中...
                  </span>
                  <span v-else class="flex items-center">
                    <PaperAirplaneIcon class="h-5 w-5 mr-2" />
                    发送消息
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 联系我们页面样式 */
</style>
