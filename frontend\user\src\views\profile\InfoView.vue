<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import request from '@/utils/request'
import {
  UserCircleIcon,
  CameraIcon,
  InformationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()
const toast = useToast()

// 表单数据
const form = reactive({
  name: '',
  email: '',
  phone: '',
  company: '',
  position: '',
  website: '',
  bio: '',
  avatar: ''
})

// 状态
const loading = ref(false)
const uploading = ref(false)
const errors = ref<Record<string, string>>({})

// 生命周期
onMounted(() => {
  loadUserInfo()
})

// 加载用户信息
const loadUserInfo = () => {
  if (authStore.user) {
    Object.assign(form, {
      name: authStore.user.name || '',
      email: authStore.user.email || '',
      phone: authStore.user.phone || '',
      company: authStore.user.company || '',
      position: authStore.user.position || '',
      website: authStore.user.website || '',
      bio: authStore.user.bio || '',
      avatar: authStore.user.avatar_url || 'http://localhost/images/default-avatar.svg'
    })
  }
}

// 头像上传
const avatarInput = ref<HTMLInputElement | null>(null)

const selectAvatar = () => {
  avatarInput.value?.click()
}

const handleAvatarChange = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    toast.error('请选择图片文件')
    return
  }

  // 验证文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    toast.error('图片大小不能超过2MB')
    return
  }

  uploading.value = true
  try {
    // 上传头像到后端
    const formData = new FormData()
    formData.append('avatar', file)

    const res = await request.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if ((res as any).success) {
      const data = (res as any).data as { avatar_url: string }
      form.avatar = data.avatar_url
      authStore.updateUserInfo({ avatar_url: data.avatar_url } as any)
      toast.success('头像上传成功')
    } else {
      toast.error((res as any).message || '头像上传失败')
    }
  } catch (error) {
    console.error('Avatar upload error:', error)
    toast.error('头像上传失败')
  } finally {
    uploading.value = false
  }
}

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string> = {}

  if (!form.name.trim()) {
    newErrors.name = '姓名不能为空'
  }

  if (!form.email.trim()) {
    newErrors.email = '邮箱不能为空'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = '邮箱格式不正确'
  }

  if (form.phone && !/^1[3-9]\d{9}$/.test(form.phone)) {
    newErrors.phone = '手机号格式不正确'
  }

  if (form.website && !/^https?:\/\/.+/.test(form.website)) {
    newErrors.website = '网站地址格式不正确'
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 保存用户信息
const saveUserInfo = async () => {
  if (!validateForm()) {
    toast.error('请检查表单中的错误')
    return
  }

  loading.value = true
  try {
    // 提交到后端再更新（保持真实流程）
    const response = await request.put('/user/profile', form)
    if ((response as any).success) {
      authStore.user = { ...(authStore.user as any), ...form } as any
      localStorage.setItem('user', JSON.stringify(authStore.user))
      toast.success('个人信息保存成功')
    } else {
      toast.error((response as any).message || '保存失败')
      return
    }
  } catch (error) {
    console.error('Save user info error:', error)
    toast.error('保存失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  loadUserInfo()
  errors.value = {}
  toast.info('已重置为原始信息')
}
</script>

<template>
  <div class="h-full p-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">个人信息</h1>
      <p class="mt-1 text-sm text-gray-500">管理您的个人资料和基本信息</p>
    </div>

    <form @submit.prevent="saveUserInfo" class="space-y-8">
      <!-- 头像设置 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <UserCircleIcon class="h-5 w-5 mr-2" />
            头像设置
          </h3>
        </div>
        <div class="px-6 py-6">
          <div class="flex items-center space-x-6">
            <div class="relative">
              <div class="h-24 w-24 rounded-full overflow-hidden bg-gray-100">
                <img
                  v-if="form.avatar"
                  :src="form.avatar"
                  alt="用户头像"
                  class="h-full w-full object-cover"
                />
                <div v-else class="h-full w-full flex items-center justify-center">
                  <UserCircleIcon class="h-12 w-12 text-gray-400" />
                </div>
              </div>
              <button
                type="button"
                @click="selectAvatar"
                :disabled="uploading"
                class="absolute bottom-0 right-0 h-8 w-8 rounded-full bg-primary-600 text-white flex items-center justify-center hover:bg-primary-700 disabled:opacity-50"
              >
                <CameraIcon class="h-4 w-4" />
              </button>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900">更换头像</h4>
              <p class="text-sm text-gray-500">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
              <button
                type="button"
                @click="selectAvatar"
                :disabled="uploading"
                class="mt-2 inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                {{ uploading ? '上传中...' : '选择文件' }}
              </button>
            </div>
          </div>
          <input
            ref="avatarInput"
            type="file"
            accept="image/*"
            @change="handleAvatarChange"
            class="hidden"
          />
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <InformationCircleIcon class="h-5 w-5 mr-2" />
            基本信息
          </h3>
        </div>
        <div class="px-6 py-6 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">姓名 *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.name ? 'border-red-300' : ''
                ]"
                placeholder="请输入您的姓名"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">邮箱 *</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.email ? 'border-red-300' : ''
                ]"
                placeholder="请输入您的邮箱"
              />
              <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700">手机号</label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.phone ? 'border-red-300' : ''
                ]"
                placeholder="请输入您的手机号"
              />
              <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
            </div>

            <div>
              <label for="website" class="block text-sm font-medium text-gray-700">个人网站</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.website ? 'border-red-300' : ''
                ]"
                placeholder="https://example.com"
              />
              <p v-if="errors.website" class="mt-1 text-sm text-red-600">{{ errors.website }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="company" class="block text-sm font-medium text-gray-700">公司</label>
              <input
                id="company"
                v-model="form.company"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                placeholder="请输入您的公司名称"
              />
            </div>

            <div>
              <label for="position" class="block text-sm font-medium text-gray-700">职位</label>
              <input
                id="position"
                v-model="form.position"
                type="text"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                placeholder="请输入您的职位"
              />
            </div>
          </div>

          <div>
            <label for="bio" class="block text-sm font-medium text-gray-700">个人简介</label>
            <textarea
              id="bio"
              v-model="form.bio"
              rows="4"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              placeholder="简单介绍一下您自己..."
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          @click="resetForm"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          重置
        </button>
        <button
          type="submit"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          <CheckCircleIcon v-if="!loading" class="h-4 w-4 mr-2" />
          <span v-if="loading">保存中...</span>
          <span v-else>保存更改</span>
        </button>
      </div>
    </form>
  </div>
</template>

<style scoped>
/* 个人信息页面样式 */
</style>
