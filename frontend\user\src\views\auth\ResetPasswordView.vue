<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { 
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'
import { authApi } from '@/api/auth'

const route = useRoute()
const router = useRouter()
const toast = useToast()

// 表单数据
const form = reactive({
  token: '',
  email: '',
  password: '',
  password_confirmation: ''
})

// 表单状态
const loading = ref(false)
const resetSuccess = ref(false)
const showPassword = ref(false)
const showPasswordConfirmation = ref(false)
const errors = ref<Record<string, string[]>>({})

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string[]> = {}

  // 密码验证
  if (!form.password) {
    newErrors.password = ['密码不能为空']
  } else if (form.password.length < 8) {
    newErrors.password = ['密码至少需要8个字符']
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)) {
    newErrors.password = ['密码必须包含大小写字母和数字']
  }

  // 确认密码验证
  if (!form.password_confirmation) {
    newErrors.password_confirmation = ['请确认密码']
  } else if (form.password !== form.password_confirmation) {
    newErrors.password_confirmation = ['两次输入的密码不一致']
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 计算属性
const isFormValid = computed(() => {
  return form.password && 
         form.password_confirmation && 
         Object.keys(errors.value).length === 0
})

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true
    const response = await authApi.resetPassword({
      token: form.token,
      email: form.email,
      password: form.password,
      password_confirmation: form.password_confirmation
    })
    
    if (response.success) {
      resetSuccess.value = true
      toast.success('密码重置成功，请使用新密码登录')
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push('/auth/login')
      }, 3000)
    } else {
      toast.error(response.message || '重置失败，请稍后重试')
    }
  } catch (error: any) {
    console.error('Reset password error:', error)
    
    // 处理服务器验证错误
    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message)
    } else {
      toast.error('重置失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 清除字段错误
const clearFieldError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}

// 组件挂载时获取URL参数
onMounted(() => {
  form.token = route.query.token as string || ''
  form.email = route.query.email as string || ''
  
  if (!form.token || !form.email) {
    toast.error('重置链接无效或已过期')
    router.push('/auth/forgot-password')
  }
})
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div>
        <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">API</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          {{ resetSuccess ? '重置成功' : '设置新密码' }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ resetSuccess ? '密码已成功重置' : '请为您的账户设置新密码' }}
        </p>
      </div>

      <!-- 成功状态 -->
      <div v-if="resetSuccess" class="bg-white rounded-lg shadow p-8">
        <div class="text-center">
          <CheckCircleIcon class="mx-auto h-16 w-16 text-green-500 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">密码重置成功</h3>
          <p class="text-gray-600 mb-6">
            您的密码已成功重置。现在您可以使用新密码登录您的账户。
          </p>
          
          <div class="space-y-4">
            <button
              @click="router.push('/auth/login')"
              class="w-full btn-primary"
            >
              立即登录
            </button>
            
            <p class="text-sm text-gray-500">
              页面将在 3 秒后自动跳转到登录页面
            </p>
          </div>
        </div>
      </div>

      <!-- 表单状态 -->
      <form v-else class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="bg-white rounded-lg shadow p-8 space-y-6">
          <!-- 邮箱显示 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              重置账户
            </label>
            <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
              <p class="text-sm text-gray-900">{{ form.email }}</p>
            </div>
          </div>

          <!-- 新密码 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
              新密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <LockClosedIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="new-password"
                required
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.password }"
                placeholder="请输入新密码"
                @input="clearFieldError('password')"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  type="button"
                  class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500"
                  @click="showPassword = !showPassword"
                >
                  <EyeIcon v-if="!showPassword" class="h-5 w-5" />
                  <EyeSlashIcon v-else class="h-5 w-5" />
                </button>
              </div>
            </div>
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password[0] }}</p>
            <p class="mt-1 text-xs text-gray-500">密码至少8位，包含大小写字母和数字</p>
          </div>

          <!-- 确认新密码 -->
          <div>
            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">
              确认新密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <LockClosedIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password_confirmation"
                v-model="form.password_confirmation"
                :type="showPasswordConfirmation ? 'text' : 'password'"
                autocomplete="new-password"
                required
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.password_confirmation }"
                placeholder="请再次输入新密码"
                @input="clearFieldError('password_confirmation')"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  type="button"
                  class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500"
                  @click="showPasswordConfirmation = !showPasswordConfirmation"
                >
                  <EyeIcon v-if="!showPasswordConfirmation" class="h-5 w-5" />
                  <EyeSlashIcon v-else class="h-5 w-5" />
                </button>
              </div>
            </div>
            <p v-if="errors.password_confirmation" class="mt-1 text-sm text-red-600">{{ errors.password_confirmation[0] }}</p>
          </div>

          <!-- 提交按钮 -->
          <div>
            <button
              type="submit"
              :disabled="loading || !isFormValid"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? '重置中...' : '重置密码' }}
            </button>
          </div>
        </div>
      </form>

      <!-- 安全提示 -->
      <div class="mt-6">
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div class="text-sm text-blue-700">
            <h4 class="font-medium mb-2">安全提示</h4>
            <ul class="space-y-1">
              <li>• 请设置一个强密码以保护您的账户安全</li>
              <li>• 不要与他人分享您的密码</li>
              <li>• 建议定期更换密码</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}
</style>
