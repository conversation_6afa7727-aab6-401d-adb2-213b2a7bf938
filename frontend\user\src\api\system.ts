// Copyright (c) 2025 Aoki. All rights reserved.

import request from '@/utils/request'

// 系统设置数据类型
export interface SystemSettings {
  basic: {
    systemName: string
    systemDescription: string
    systemVersion: string
    adminEmail: string
    timezone: string
    defaultLanguage: string
  }
  api: {
    baseUrl: string
    version: string
    timeout: number
    maxConcurrency: number
    enableCache: boolean
    cacheExpiration: number
  }
  homepage?: {
    trustedData?: {
      api_calls: number
      developers: number
      apis: number
      availability: number
    }
  }

}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message: string
  timestamp?: string
}

export const systemApi = {
  /**
   * 获取公开的系统设置（用户端可见的设置）
   */
  getPublicSettings: async (): Promise<ApiResponse<SystemSettings>> => {
    return request.get('/public-settings')
  },

  /**
   * 获取系统状态信息
   */
  getSystemStatus: async (): Promise<ApiResponse<any>> => {
    return request.get('/system-status')
  }
}
