// Copyright (c) 2025 Aoki. All rights reserved.

export interface User {
  id: number
  name: string
  email: string
  avatar?: string
  avatar_url?: string
  email_verified_at?: string
  created_at: string
  updated_at: string
  phone?: string
  company?: string
  position?: string
  website?: string
  bio?: string
}

export interface LoginCredentials {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  name: string
  email: string
  password: string
  password_confirmation: string
  phone?: string
  company?: string
  agreement: boolean
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: User
    token: string
    expires_at: string
  }
}

export interface UserResponse {
  success: boolean
  message: string
  data: {
    user: User
  }
}
