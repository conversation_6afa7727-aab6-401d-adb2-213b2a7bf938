<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  BookOpenIcon,
  CodeBracketIcon,
  TagIcon,
  ClockIcon,
  StarIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/vue/24/outline'

// 状态管理
const loading = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedMethod = ref('all')

// 分类数据
const categories = ref([
  { id: 'all', name: '全部', count: 12 },
  { id: 'auth', name: '认证', count: 3 },
  { id: 'data', name: '数据', count: 4 },
  { id: 'file', name: '文件', count: 2 },
  { id: 'payment', name: '支付', count: 2 },
  { id: 'notification', name: '通知', count: 1 }
])

// 请求方法
const methods = ref([
  { id: 'all', name: '全部方法' },
  { id: 'GET', name: 'GET' },
  { id: 'POST', name: 'POST' },
  { id: 'PUT', name: 'PUT' },
  { id: 'DELETE', name: 'DELETE' }
])

// 模拟API数据
const apis = ref([
  {
    id: 1,
    name: '用户认证API',
    description: '提供用户登录、注册、密码重置等认证功能',
    endpoint: '/api/auth/login',
    method: 'POST',
    version: 'v1.0',
    category: 'auth',
    status: 'stable',
    rating: 4.8,
    views: 1250,
    lastUpdated: '2025-01-10',
    provider: 'API平台',
    tags: ['认证', '用户', '安全']
  },
  {
    id: 2,
    name: '用户信息API',
    description: '获取和更新用户基本信息',
    endpoint: '/api/users/{id}',
    method: 'GET',
    version: 'v1.2',
    category: 'data',
    status: 'stable',
    rating: 4.6,
    views: 980,
    lastUpdated: '2025-01-08',
    provider: 'API平台',
    tags: ['用户', '数据', '信息']
  },
  {
    id: 3,
    name: '文件上传API',
    description: '支持多种格式的文件上传功能',
    endpoint: '/api/files/upload',
    method: 'POST',
    version: 'v2.0',
    category: 'file',
    status: 'stable',
    rating: 4.9,
    views: 2100,
    lastUpdated: '2025-01-12',
    provider: 'API平台',
    tags: ['文件', '上传', '存储']
  },
  {
    id: 4,
    name: '支付处理API',
    description: '安全的在线支付处理接口',
    endpoint: '/api/payments/process',
    method: 'POST',
    version: 'v1.5',
    category: 'payment',
    status: 'stable',
    rating: 4.7,
    views: 1580,
    lastUpdated: '2025-01-09',
    provider: 'API平台',
    tags: ['支付', '交易', '安全']
  },
  {
    id: 5,
    name: '数据分析API',
    description: '提供数据统计和分析功能',
    endpoint: '/api/analytics/stats',
    method: 'GET',
    version: 'v1.1',
    category: 'data',
    status: 'beta',
    rating: 4.3,
    views: 750,
    lastUpdated: '2025-01-11',
    provider: 'API平台',
    tags: ['分析', '统计', '数据']
  },
  {
    id: 6,
    name: '消息通知API',
    description: '发送邮件、短信等通知消息',
    endpoint: '/api/notifications/send',
    method: 'POST',
    version: 'v1.0',
    category: 'notification',
    status: 'stable',
    rating: 4.5,
    views: 650,
    lastUpdated: '2025-01-07',
    provider: 'API平台',
    tags: ['通知', '消息', '邮件']
  }
])

// 计算属性
const filteredApis = computed(() => {
  let result = apis.value

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter(api => api.category === selectedCategory.value)
  }

  // 按请求方法筛选
  if (selectedMethod.value !== 'all') {
    result = result.filter(api => api.method === selectedMethod.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(api =>
      api.name.toLowerCase().includes(query) ||
      api.description.toLowerCase().includes(query) ||
      api.endpoint.toLowerCase().includes(query) ||
      api.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const getStatusColor = (status: string) => {
  switch (status) {
    case 'stable': return 'bg-green-100 text-green-800'
    case 'beta': return 'bg-yellow-100 text-yellow-800'
    case 'deprecated': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getMethodColor = (method: string) => {
  switch (method) {
    case 'GET': return 'bg-blue-100 text-blue-800'
    case 'POST': return 'bg-green-100 text-green-800'
    case 'PUT': return 'bg-yellow-100 text-yellow-800'
    case 'DELETE': return 'bg-red-100 text-red-800'
    case 'PATCH': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const viewApiDetail = (api: any) => {
  // 跳转到API详情页面
  window.open(`/docs/api/${api.id}`, '_blank')
}

// 生命周期
onMounted(() => {
  loading.value = true
  // 模拟加载延迟
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-gray-900">API接口列表</h1>
          <p class="mt-4 text-lg text-gray-600">
            发现和使用我们提供的各种API接口，助力您的应用开发
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 侧边栏 - 筛选器 -->
        <div class="lg:w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">分类筛选</h3>
            <div class="space-y-2">
              <button
                v-for="category in categories"
                :key="category.id"
                @click="selectedCategory = category.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  selectedCategory === category.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center justify-between">
                  <span>{{ category.name }}</span>
                  <span class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                    {{ category.count }}
                  </span>
                </div>
              </button>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">请求方法</h3>
            <div class="space-y-2">
              <button
                v-for="method in methods"
                :key="method.id"
                @click="selectedMethod = method.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  selectedMethod === method.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                {{ method.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1">
          <!-- 搜索和筛选 -->
          <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <div class="relative">
                  <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="搜索API名称、描述、端点或标签..."
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <FunnelIcon class="h-4 w-4 mr-2" />
                高级筛选
              </button>
            </div>
          </div>

          <!-- API列表 -->
          <div class="space-y-6">
            <div v-if="loading" class="text-center py-12">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p class="mt-2 text-sm text-gray-500">加载中...</p>
            </div>

            <div v-else-if="filteredApis.length === 0" class="text-center py-12">
              <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">暂无API接口</h3>
              <p class="mt-1 text-sm text-gray-500">尝试调整搜索条件或筛选器</p>
            </div>

            <div
              v-else
              v-for="api in filteredApis"
              :key="api.id"
              class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
              @click="viewApiDetail(api)"
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-2">
                      <h3 class="text-lg font-medium text-gray-900">{{ api.name }}</h3>
                      <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusColor(api.status)]">
                        {{ api.status }}
                      </span>
                      <span :class="['px-2 py-1 text-xs font-medium rounded-full', getMethodColor(api.method)]">
                        {{ api.method }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">{{ api.description }}</p>
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      <div class="flex items-center">
                        <CodeBracketIcon class="h-4 w-4 mr-1" />
                        <code class="bg-gray-100 px-2 py-1 rounded text-xs">{{ api.endpoint }}</code>
                      </div>
                      <div class="flex items-center">
                        <TagIcon class="h-4 w-4 mr-1" />
                        {{ api.version }}
                      </div>
                      <div class="flex items-center">
                        <ClockIcon class="h-4 w-4 mr-1" />
                        {{ api.lastUpdated }}
                      </div>
                    </div>

                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                      <div class="flex items-center">
                        <StarIcon class="h-4 w-4 mr-1 text-yellow-400" />
                        {{ api.rating }}
                      </div>
                      <div class="flex items-center">
                        <EyeIcon class="h-4 w-4 mr-1" />
                        {{ api.views }} 次查看
                      </div>
                      <div class="flex items-center">
                        <span class="text-gray-400">by</span>
                        <span class="ml-1 font-medium">{{ api.provider }}</span>
                      </div>
                    </div>

                    <div class="mt-3 flex flex-wrap gap-2">
                      <span
                        v-for="tag in api.tags"
                        :key="tag"
                        class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <ArrowTopRightOnSquareIcon class="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* API列表页面样式 */
</style>
