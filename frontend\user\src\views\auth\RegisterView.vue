<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  EyeIcon,
  EyeSlashIcon,
  UserIcon,
  EnvelopeIcon,
  LockClosedIcon,
  PhoneIcon,
  BuildingOfficeIcon
} from '@heroicons/vue/24/outline'
import type { RegisterData } from '@/types/auth'

const authStore = useAuthStore()

// 表单数据
const form = reactive<RegisterData>({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  phone: '',
  company: '',
  agreement: false
})

// 表单状态
const loading = ref(false)
const showPassword = ref(false)
const showPasswordConfirmation = ref(false)
const errors = ref<Record<string, string[]>>({})

// 表单验证规则
const validateForm = () => {
  const newErrors: Record<string, string[]> = {}

  // 姓名验证
  if (!form.name.trim()) {
    newErrors.name = ['姓名不能为空']
  } else if (form.name.trim().length < 2) {
    newErrors.name = ['姓名至少需要2个字符']
  }

  // 邮箱验证
  if (!form.email.trim()) {
    newErrors.email = ['邮箱不能为空']
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = ['请输入有效的邮箱地址']
  }

  // 密码验证
  if (!form.password) {
    newErrors.password = ['密码不能为空']
  } else if (form.password.length < 8) {
    newErrors.password = ['密码至少需要8个字符']
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)) {
    newErrors.password = ['密码必须包含大小写字母和数字']
  }

  // 确认密码验证
  if (!form.password_confirmation) {
    newErrors.password_confirmation = ['请确认密码']
  } else if (form.password !== form.password_confirmation) {
    newErrors.password_confirmation = ['两次输入的密码不一致']
  }

  // 手机号验证（可选）
  if (form.phone && !/^1[3-9]\d{9}$/.test(form.phone)) {
    newErrors.phone = ['请输入有效的手机号码']
  }

  // 协议验证
  if (!form.agreement) {
    newErrors.agreement = ['请阅读并同意用户协议和隐私政策']
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 计算属性
const isFormValid = computed(() => {
  return form.name &&
         form.email &&
         form.password &&
         form.password_confirmation &&
         form.agreement &&
         Object.keys(errors.value).length === 0
})

// 提交注册
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true
    await authStore.register(form)
  } catch (error: any) {
    console.error('Register error:', error)
    // 处理服务器验证错误
    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors
    }
  } finally {
    loading.value = false
  }
}

// 清除字段错误
const clearFieldError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}
</script>

<template>
  <div class="relative z-10 min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div>
        <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">API</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          注册新账户
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          已有账户？
          <RouterLink to="/auth/login" class="font-medium text-primary-600 hover:text-primary-500">
            立即登录
          </RouterLink>
        </p>
      </div>

      <!-- 注册表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="bg-white rounded-lg shadow p-8 space-y-6">
          <!-- 姓名 -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              姓名 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <UserIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="name"
                v-model="form.name"
                type="text"
                autocomplete="name"
                required
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.name }"
                placeholder="请输入您的姓名"
                @input="clearFieldError('name')"
              />
            </div>
            <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name[0] }}</p>
          </div>

          <!-- 邮箱 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
              邮箱地址 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <EnvelopeIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="email"
                v-model="form.email"
                type="email"
                autocomplete="email"
                required
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.email }"
                placeholder="请输入邮箱地址"
                @input="clearFieldError('email')"
              />
            </div>
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email[0] }}</p>
          </div>

          <!-- 密码 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
              密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <LockClosedIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="new-password"
                required
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.password }"
                placeholder="请输入密码"
                @input="clearFieldError('password')"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  type="button"
                  class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500"
                  @click="showPassword = !showPassword"
                >
                  <EyeIcon v-if="!showPassword" class="h-5 w-5" />
                  <EyeSlashIcon v-else class="h-5 w-5" />
                </button>
              </div>
            </div>
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password[0] }}</p>
            <p class="mt-1 text-xs text-gray-500">密码至少8位，包含大小写字母和数字</p>
          </div>

          <!-- 确认密码 -->
          <div>
            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">
              确认密码 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <LockClosedIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password_confirmation"
                v-model="form.password_confirmation"
                :type="showPasswordConfirmation ? 'text' : 'password'"
                autocomplete="new-password"
                required
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.password_confirmation }"
                placeholder="请再次输入密码"
                @input="clearFieldError('password_confirmation')"
              />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  type="button"
                  class="text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500"
                  @click="showPasswordConfirmation = !showPasswordConfirmation"
                >
                  <EyeIcon v-if="!showPasswordConfirmation" class="h-5 w-5" />
                  <EyeSlashIcon v-else class="h-5 w-5" />
                </button>
              </div>
            </div>
            <p v-if="errors.password_confirmation" class="mt-1 text-sm text-red-600">{{ errors.password_confirmation[0] }}</p>
          </div>

          <!-- 手机号（可选） -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
              手机号码 <span class="text-gray-400 text-xs">(可选)</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <PhoneIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                autocomplete="tel"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.phone }"
                placeholder="请输入手机号码"
                @input="clearFieldError('phone')"
              />
            </div>
            <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone[0] }}</p>
          </div>

          <!-- 公司（可选） -->
          <div>
            <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
              公司名称 <span class="text-gray-400 text-xs">(可选)</span>
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <BuildingOfficeIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="company"
                v-model="form.company"
                type="text"
                autocomplete="organization"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入公司名称"
              />
            </div>
          </div>

          <!-- 用户协议 -->
          <div>
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="agreement"
                  v-model="form.agreement"
                  type="checkbox"
                  class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                  :class="{ 'border-red-300': errors.agreement }"
                  @change="clearFieldError('agreement')"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="agreement" class="text-gray-700">
                  我已阅读并同意
                  <a href="#" class="text-primary-600 hover:text-primary-500">用户协议</a>
                  和
                  <a href="#" class="text-primary-600 hover:text-primary-500">隐私政策</a>
                  <span class="text-red-500">*</span>
                </label>
              </div>
            </div>
            <p v-if="errors.agreement" class="mt-1 text-sm text-red-600">{{ errors.agreement[0] }}</p>
          </div>

          <!-- 提交按钮 -->
          <div>
            <button
              type="submit"
              :disabled="loading || !isFormValid"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? '注册中...' : '注册账户' }}
            </button>
          </div>
        </div>
      </form>

      <!-- 其他登录方式 -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 text-gray-500">或者</span>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            注册即表示您同意我们的服务条款和隐私政策
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-outline {
  @apply border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}
</style>
