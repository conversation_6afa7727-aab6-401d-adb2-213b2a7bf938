<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { RouterLink } from 'vue-router'

const footerNavigation = {
  main: [
    { name: '首页', href: '/' },
    { name: 'API列表', href: '/apis' },
    { name: '开发文档', href: '/docs' },
    { name: '关于我们', href: '/about' },
  ],
  support: [
    { name: '帮助中心', href: '/help' },
    { name: '联系我们', href: '/contact' },
    { name: '服务状态', href: '/status' },
    { name: 'API状态', href: '/api-status' },
  ],
  company: [
    { name: '关于', href: '/about' },
    { name: '博客', href: '/blog' },
    { name: '招聘', href: '/careers' },
    { name: '新闻', href: '/news' },
  ],
  legal: [
    { name: '隐私政策', href: '/privacy' },
    { name: '服务条款', href: '/terms' },
    { name: '<PERSON><PERSON>政策', href: '/cookies' },
    { name: '许可证', href: '/license' },
  ],
}

const socialLinks = [
  {
    name: 'GitHub',
    href: '#',
    iconPath: 'M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z'
  },
  {
    name: 'Twitter',
    href: '#',
    iconPath: 'M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84'
  },
]
</script>

<template>
  <footer class="bg-white border-t border-gray-200">
    <div class="mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8">
      <div class="grid grid-cols-2 gap-8 xl:col-span-2 xl:grid-cols-4">
        <!-- 主要导航 -->
        <div>
          <h3 class="text-sm font-semibold leading-6 text-gray-900">产品</h3>
          <ul role="list" class="mt-6 space-y-4">
            <li v-for="item in footerNavigation.main" :key="item.name">
              <RouterLink :to="item.href" class="text-sm leading-6 text-gray-600 hover:text-gray-900">
                {{ item.name }}
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- 支持 -->
        <div>
          <h3 class="text-sm font-semibold leading-6 text-gray-900">支持</h3>
          <ul role="list" class="mt-6 space-y-4">
            <li v-for="item in footerNavigation.support" :key="item.name">
              <RouterLink :to="item.href" class="text-sm leading-6 text-gray-600 hover:text-gray-900">
                {{ item.name }}
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- 公司 -->
        <div>
          <h3 class="text-sm font-semibold leading-6 text-gray-900">公司</h3>
          <ul role="list" class="mt-6 space-y-4">
            <li v-for="item in footerNavigation.company" :key="item.name">
              <RouterLink :to="item.href" class="text-sm leading-6 text-gray-600 hover:text-gray-900">
                {{ item.name }}
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- 法律 -->
        <div>
          <h3 class="text-sm font-semibold leading-6 text-gray-900">法律</h3>
          <ul role="list" class="mt-6 space-y-4">
            <li v-for="item in footerNavigation.legal" :key="item.name">
              <RouterLink :to="item.href" class="text-sm leading-6 text-gray-600 hover:text-gray-900">
                {{ item.name }}
              </RouterLink>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="border-t border-gray-200 bg-gray-50">
      <div class="mx-auto max-w-7xl px-6 py-6 md:flex md:items-center md:justify-between lg:px-8">
        <div class="flex justify-center space-x-6 md:order-2">
          <a
            v-for="item in socialLinks"
            :key="item.name"
            :href="item.href"
            class="text-gray-400 hover:text-gray-500"
          >
            <span class="sr-only">{{ item.name }}</span>
            <svg fill="currentColor" viewBox="0 0 24 24" class="h-6 w-6" aria-hidden="true">
              <path :d="item.iconPath" fill-rule="evenodd" clip-rule="evenodd" />
            </svg>
          </a>
        </div>
        <div class="mt-8 md:order-1 md:mt-0">
          <p class="text-center text-xs leading-5 text-gray-500">
            &copy; 2025 API平台. 保留所有权利.
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
/* Footer specific styles */
</style>
