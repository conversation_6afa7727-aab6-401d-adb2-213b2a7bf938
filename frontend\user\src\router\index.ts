// Copyright (c) 2025 Aoki. All rights reserved.

import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/HomeView.vue'),
      meta: {
        title: '首页',
        requiresAuth: false
      }
    },

    // 公共页面路由 - 无需认证
    {
      path: '/apis',
      name: 'apis',
      component: () => import('@/views/ApisView.vue'),
      meta: {
        title: 'API列表',
        requiresAuth: false
      }
    },
    {
      path: '/docs',
      name: 'docs',
      component: () => import('@/views/DocsView.vue'),
      meta: {
        title: '开发文档',
        requiresAuth: false
      }
    },
    {
      path: '/docs/api/:id',
      name: 'api-detail',
      component: () => import('@/views/ApiDetailView.vue'),
      meta: {
        title: 'API详情',
        requiresAuth: false
      }
    },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('@/views/ContactView.vue'),
      meta: {
        title: '联系我们',
        requiresAuth: false
      }
    },
    {
      path: '/examples',
      name: 'examples',
      component: () => import('@/views/ExamplesView.vue'),
      meta: {
        title: 'API示例',
        requiresAuth: false
      }
    },

    // 主要导航路由 - 需要认证
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: {
        title: '控制台',
        requiresAuth: true
      }
    },
    {
      path: '/developer',
      name: 'developer',
      redirect: '/developer/apis',
      meta: {
        title: '开发者中心',
        requiresAuth: true
      },
      children: [
        {
          path: 'apis',
          name: 'developer-apis',
          component: () => import('@/views/developer/ApiListView.vue'),
          meta: {
            title: '接口列表',
            requiresAuth: true
          }
        },
        {
          path: 'submit',
          name: 'developer-submit',
          component: () => import('@/views/developer/SubmitApiView.vue'),
          meta: {
            title: '提交接口',
            requiresAuth: true
          }
        }
      ]
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('@/views/profile/ProfileLayout.vue'),
      redirect: '/profile/info',
      meta: {
        title: '个人设置',
        requiresAuth: true
      },
      children: [
        {
          path: 'info',
          name: 'profile-info',
          component: () => import('@/views/profile/InfoView.vue'),
          meta: {
            title: '个人信息',
            requiresAuth: true
          }
        },
        {
          path: 'security',
          name: 'profile-security',
          component: () => import('@/views/profile/SecurityView.vue'),
          meta: {
            title: '安全设置',
            requiresAuth: true
          }
        }
      ]
    },

    // 认证路由组
    {
      path: '/auth/login',
      name: 'login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false,
        hideForAuth: true
      }
    },
    {
      path: '/auth/register',
      name: 'register',
      component: () => import('@/views/auth/RegisterView.vue'),
      meta: {
        title: '注册',
        requiresAuth: false,
        hideForAuth: true
      }
    },
    {
      path: '/auth/forgot-password',
      name: 'forgot-password',
      component: () => import('@/views/auth/ForgotPasswordView.vue'),
      meta: {
        title: '忘记密码',
        requiresAuth: false,
        hideForAuth: true
      }
    },
    {
      path: '/auth/reset-password',
      name: 'reset-password',
      component: () => import('@/views/auth/ResetPasswordView.vue'),
      meta: {
        title: '重置密码',
        requiresAuth: false,
        hideForAuth: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
      meta: {
        title: '页面未找到',
        requiresAuth: false
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - API平台` : 'API平台'
  
  // 检查认证状态
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }
  
  // 已登录用户访问登录/注册页面时重定向到控制台
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }
  
  next()
})

export default router
