<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import DashboardLayout from './DashboardLayout.vue'

const route = useRoute()

// 判断是否为认证页面
const isAuthPage = computed(() => {
  return route.path.startsWith('/auth/')
})

// 判断是否为用户控制台页面
const isDashboardPage = computed(() => {
  return route.path.startsWith('/dashboard') ||
         route.path.startsWith('/profile') ||
         route.path.startsWith('/api-keys') ||
         route.path.startsWith('/developer')
})
</script>

<template>
  <!-- 控制台页面使用专门的布局 -->
  <DashboardLayout v-if="isDashboardPage && !isAuthPage">
    <slot />
  </DashboardLayout>

  <!-- 公共页面使用标准布局 -->
  <div v-else class="min-h-screen flex flex-col">
    <!-- 头部导航 -->
    <AppHeader v-if="!isAuthPage" />

    <!-- 主要内容区域 -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- 底部 -->
    <AppFooter v-if="!isAuthPage && !isDashboardPage" />
  </div>
</template>

<style scoped>
/* 布局样式 */
</style>
