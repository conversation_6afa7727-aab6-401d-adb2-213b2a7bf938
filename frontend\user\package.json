{"name": "api-platform-user", "version": "1.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite --port 3001", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^11.3.0", "axios": "^1.7.9", "pinia": "^2.2.6", "vue": "^3.5.18", "vue-router": "^4.4.5", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "postcss": "^8.5.1", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-compression2": "^2.2.0", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^2.1.10"}}