<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { useAuthStore } from '@/stores/auth'
import request from '@/utils/request'
import {
  ShieldCheckIcon,
  KeyIcon,
  BellIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  EyeIcon,
  EyeSlashIcon,
  XMarkIcon,
  EnvelopeIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'

const toast = useToast()
const authStore = useAuthStore()

// 模态框状态
const showPasswordModal = ref(false)
const passwordStep = ref(1) // 1: 输入密码, 2: 邮箱验证, 3: 完成

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
  verificationCode: ''
})

// 通知设置 - 不设置默认值，避免闪烁
const notificationSettings = reactive({
  emailNotifications: false,
  smsNotifications: false,
  apiKeyExpiry: false,
  securityAlerts: false,
  weeklyReport: false
})

// 初始化状态
const settingsLoading = ref(true) // 初始为true，表示正在加载
const settingsLoaded = ref(false) // 标记数据是否已加载

// 状态
const passwordLoading = ref(false)
const notificationLoading = ref(false)
const emailVerificationLoading = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const passwordErrors = ref<Record<string, string>>({})
const verificationSent = ref(false)
const verificationCountdown = ref(0)

// 计算属性
const userEmail = computed(() => authStore.user?.email || '<EMAIL>')
const maskedEmail = computed(() => {
  const email = userEmail.value
  const [username, domain] = email.split('@')
  const maskedUsername = username.length > 2
    ? username.substring(0, 2) + '*'.repeat(username.length - 2)
    : username
  return `${maskedUsername}@${domain}`
})

// 模拟登录设备数据
const loginDevices = ref([
  {
    id: 1,
    device: 'Windows PC',
    browser: 'Chrome 120.0',
    location: '北京, 中国',
    lastActive: '2025-01-15 14:30',
    current: true
  },
  {
    id: 2,
    device: 'iPhone 15',
    browser: 'Safari',
    location: '上海, 中国',
    lastActive: '2025-01-14 09:15',
    current: false
  }
])

// 密码强度检查
const getPasswordStrength = (password: string) => {
  let strength = 0
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++

  return strength
}

const getPasswordStrengthText = (strength: number) => {
  switch (strength) {
    case 0:
    case 1: return { text: '弱', color: 'text-red-600' }
    case 2:
    case 3: return { text: '中等', color: 'text-yellow-600' }
    case 4:
    case 5: return { text: '强', color: 'text-green-600' }
    default: return { text: '弱', color: 'text-red-600' }
  }
}

// 验证密码表单
const validatePasswordForm = () => {
  const errors: Record<string, string> = {}

  if (!passwordForm.currentPassword) {
    errors.currentPassword = '请输入当前密码'
  }

  if (!passwordForm.newPassword) {
    errors.newPassword = '请输入新密码'
  } else if (passwordForm.newPassword.length < 8) {
    errors.newPassword = '密码长度至少8位'
  }

  if (!passwordForm.confirmPassword) {
    errors.confirmPassword = '请确认新密码'
  } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  }

  passwordErrors.value = errors
  return Object.keys(errors).length === 0
}

// 打开密码修改模态框
const openPasswordModal = () => {
  showPasswordModal.value = true
  passwordStep.value = 1
  // 重置表单
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    verificationCode: ''
  })
  passwordErrors.value = {}
  verificationSent.value = false
  verificationCountdown.value = 0
}

// 关闭密码修改模态框
const closePasswordModal = () => {
  showPasswordModal.value = false
  passwordStep.value = 1
  passwordLoading.value = false
  emailVerificationLoading.value = false
}

// 第一步：验证当前密码并发送邮箱验证
const submitPasswordChange = async () => {
  if (!validatePasswordForm()) {
    toast.error('请检查表单中的错误')
    return
  }

  passwordLoading.value = true
  try {
    // 模拟验证当前密码的API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 发送邮箱验证码
    await sendEmailVerification()

    // 进入邮箱验证步骤
    passwordStep.value = 2
    toast.success('验证邮件已发送，请查收')
  } catch (error) {
    console.error('Password validation error:', error)
    toast.error('当前密码验证失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 发送邮箱验证码
const sendEmailVerification = async () => {
  emailVerificationLoading.value = true
  try {
    // 模拟发送邮箱验证码的API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    verificationSent.value = true
    startCountdown()

    toast.success(`验证码已发送到 ${maskedEmail.value}`)
  } catch (error) {
    console.error('Send email verification error:', error)
    toast.error('发送验证码失败，请稍后重试')
  } finally {
    emailVerificationLoading.value = false
  }
}

// 重新发送验证码
const resendVerificationCode = async () => {
  if (verificationCountdown.value > 0) return
  await sendEmailVerification()
}

// 开始倒计时
const startCountdown = () => {
  verificationCountdown.value = 60
  const timer = setInterval(() => {
    verificationCountdown.value--
    if (verificationCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 验证邮箱验证码并完成密码修改
const verifyCodeAndChangePassword = async () => {
  if (!passwordForm.verificationCode) {
    passwordErrors.value = { verificationCode: '请输入验证码' }
    return
  }

  passwordLoading.value = true
  try {
    // 模拟验证码验证和密码修改的API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 进入完成步骤
    passwordStep.value = 3

    // 3秒后自动关闭模态框
    setTimeout(() => {
      closePasswordModal()
      toast.success('密码修改成功')
    }, 3000)
  } catch (error) {
    console.error('Verify code and change password error:', error)
    passwordErrors.value = { verificationCode: '验证码错误，请重试' }
  } finally {
    passwordLoading.value = false
  }
}

// 获取通知设置
const loadNotificationSettings = async () => {
  settingsLoading.value = true
  settingsLoaded.value = false
  try {
    const res = await request.get('/user/notification-settings')

    if ((res as any).success && (res as any).data) {
      Object.assign(notificationSettings, (res as any).data)
      console.log('Notification settings loaded:', (res as any).data)
      settingsLoaded.value = true
    } else {
      console.warn('Invalid response format, using default settings')
      settingsLoaded.value = true
    }
  } catch (error) {
    console.error('Load notification settings error:', error)
    // 如果加载失败，使用默认设置，不显示错误提示（避免干扰用户）
    console.warn('Using default notification settings')
    settingsLoaded.value = true
  } finally {
    settingsLoading.value = false
  }
}


// 保存通知设置
const saveNotificationSettings = async () => {
  notificationLoading.value = true
  try {
    const res = await request.put('/user/notification-settings', notificationSettings)

    if ((res as any).success) {
      console.log('Notification settings saved:', (res as any).data)
      toast.success('通知设置保存成功')
    } else {
      throw new Error(((res as any).message) || '保存失败')
    }
  } catch (error) {
    console.error('Save notification settings error:', error)
    toast.error('保存失败，请稍后重试')
  } finally {
    notificationLoading.value = false
  }
}

// 注销设备
const logoutDevice = async (deviceId: number) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    loginDevices.value = loginDevices.value.filter(device => device.id !== deviceId)
    toast.success('设备已注销')
  } catch (error) {
    console.error('Logout device error:', error)
    toast.error('注销失败，请稍后重试')
  }
}

// 注销所有其他设备
const logoutAllOtherDevices = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    loginDevices.value = loginDevices.value.filter(device => device.current)
    toast.success('已注销所有其他设备')
  } catch (error) {
    console.error('Logout all devices error:', error)
    toast.error('注销失败，请稍后重试')
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await loadNotificationSettings()
})
</script>

<template>
  <div class="h-full p-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">安全设置</h1>
      <p class="mt-1 text-sm text-gray-500">管理您的账户安全和隐私设置</p>
    </div>

    <div class="space-y-8">
      <!-- 密码修改 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <KeyIcon class="h-5 w-5 mr-2" />
            修改密码
          </h3>
        </div>
        <div class="px-6 py-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">为了保护您的账户安全，建议定期更换密码</p>
              <p class="text-xs text-gray-500 mt-1">密码修改需要邮箱验证确认</p>
            </div>
            <button
              @click="openPasswordModal"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <KeyIcon class="h-4 w-4 mr-2" />
              修改密码
            </button>
          </div>
        </div>
      </div>


      <!-- 通知设置 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <BellIcon class="h-5 w-5 mr-2" />
            通知设置
          </h3>
        </div>
        <div class="px-6 py-6">
          <!-- 加载状态 -->
          <div v-if="settingsLoading && !settingsLoaded" class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-3 text-sm text-gray-500">加载通知设置中...</span>
          </div>

          <!-- 通知设置内容 -->
          <div v-else class="space-y-6">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">邮件通知</h4>
                <p class="text-sm text-gray-500">接收重要的邮件通知</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  v-model="notificationSettings.emailNotifications"
                  type="checkbox"
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">短信通知</h4>
                <p class="text-sm text-gray-500">接收重要的短信通知</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  v-model="notificationSettings.smsNotifications"
                  type="checkbox"
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">API密钥过期提醒</h4>
                <p class="text-sm text-gray-500">在API密钥即将过期时提醒</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  v-model="notificationSettings.apiKeyExpiry"
                  type="checkbox"
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">安全警报</h4>
                <p class="text-sm text-gray-500">账户安全相关的重要警报</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  v-model="notificationSettings.securityAlerts"
                  type="checkbox"
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">周报</h4>
                <p class="text-sm text-gray-500">每周的使用统计报告</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  v-model="notificationSettings.weeklyReport"
                  type="checkbox"
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>

          <div v-if="settingsLoaded" class="mt-6 flex justify-end">
            <button
              @click="saveNotificationSettings"
              :disabled="notificationLoading || settingsLoading"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <span v-if="notificationLoading">保存中...</span>
              <span v-else>保存设置</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 登录设备管理 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <ShieldCheckIcon class="h-5 w-5 mr-2" />
              登录设备管理
            </h3>
            <button
              @click="logoutAllOtherDevices"
              class="text-sm text-red-600 hover:text-red-700"
            >
              注销所有其他设备
            </button>
          </div>
        </div>
        <div class="px-6 py-6">
          <div class="space-y-4">
            <div
              v-for="device in loginDevices"
              :key="device.id"
              class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
            >
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <DevicePhoneMobileIcon v-if="device.device.includes('iPhone')" class="h-8 w-8 text-gray-400" />
                  <ComputerDesktopIcon v-else class="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <div class="flex items-center space-x-2">
                    <h4 class="text-sm font-medium text-gray-900">{{ device.device }}</h4>
                    <span v-if="device.current" class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                      当前设备
                    </span>
                  </div>
                  <p class="text-sm text-gray-500">{{ device.browser }} • {{ device.location }}</p>
                  <p class="text-xs text-gray-400">最后活跃: {{ device.lastActive }}</p>
                </div>
              </div>
              <div v-if="!device.current">
                <button
                  @click="logoutDevice(device.id)"
                  class="text-sm text-red-600 hover:text-red-700"
                >
                  注销
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 密码修改模态框 -->
    <div
      v-if="showPasswordModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- 背景遮罩 -->
        <div
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          @click="closePasswordModal"
        ></div>

        <!-- 模态框内容 -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <!-- 头部 -->
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <KeyIcon class="h-5 w-5 mr-2" />
                修改密码
              </h3>
              <button
                @click="closePasswordModal"
                class="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon class="h-6 w-6" />
              </button>
            </div>

            <!-- 步骤指示器 -->
            <div class="flex items-center justify-center mb-6">
              <div class="flex items-center">
                <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', passwordStep >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-600']">
                  1
                </div>
                <div :class="['w-16 h-1', passwordStep >= 2 ? 'bg-primary-600' : 'bg-gray-200']"></div>
                <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', passwordStep >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-600']">
                  2
                </div>
                <div :class="['w-16 h-1', passwordStep >= 3 ? 'bg-primary-600' : 'bg-gray-200']"></div>
                <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', passwordStep >= 3 ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600']">
                  <CheckCircleIcon v-if="passwordStep >= 3" class="h-5 w-5" />
                  <span v-else>3</span>
                </div>
              </div>
            </div>

            <!-- 步骤内容 -->
            <div class="space-y-4">
              <!-- 第一步：输入密码 -->
              <div v-if="passwordStep === 1">
                <h4 class="text-sm font-medium text-gray-900 mb-4">请输入您的密码信息</h4>
                <form @submit.prevent="submitPasswordChange" class="space-y-4">
                  <div>
                    <label for="modalCurrentPassword" class="block text-sm font-medium text-gray-700">当前密码</label>
                    <div class="mt-1 relative">
                      <input
                        id="modalCurrentPassword"
                        v-model="passwordForm.currentPassword"
                        :type="showCurrentPassword ? 'text' : 'password'"
                        :class="[
                          'block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                          passwordErrors.currentPassword ? 'border-red-300' : ''
                        ]"
                        placeholder="请输入当前密码"
                      />
                      <button
                        type="button"
                        @click="showCurrentPassword = !showCurrentPassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        <EyeIcon v-if="!showCurrentPassword" class="h-5 w-5 text-gray-400" />
                        <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                    <p v-if="passwordErrors.currentPassword" class="mt-1 text-sm text-red-600">
                      {{ passwordErrors.currentPassword }}
                    </p>
                  </div>

                  <div>
                    <label for="modalNewPassword" class="block text-sm font-medium text-gray-700">新密码</label>
                    <div class="mt-1 relative">
                      <input
                        id="modalNewPassword"
                        v-model="passwordForm.newPassword"
                        :type="showNewPassword ? 'text' : 'password'"
                        :class="[
                          'block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                          passwordErrors.newPassword ? 'border-red-300' : ''
                        ]"
                        placeholder="请输入新密码"
                      />
                      <button
                        type="button"
                        @click="showNewPassword = !showNewPassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        <EyeIcon v-if="!showNewPassword" class="h-5 w-5 text-gray-400" />
                        <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                    <div v-if="passwordForm.newPassword" class="mt-2">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">密码强度:</span>
                        <span :class="['text-sm font-medium', getPasswordStrengthText(getPasswordStrength(passwordForm.newPassword)).color]">
                          {{ getPasswordStrengthText(getPasswordStrength(passwordForm.newPassword)).text }}
                        </span>
                      </div>
                    </div>
                    <p v-if="passwordErrors.newPassword" class="mt-1 text-sm text-red-600">
                      {{ passwordErrors.newPassword }}
                    </p>
                  </div>

                  <div>
                    <label for="modalConfirmPassword" class="block text-sm font-medium text-gray-700">确认新密码</label>
                    <div class="mt-1 relative">
                      <input
                        id="modalConfirmPassword"
                        v-model="passwordForm.confirmPassword"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        :class="[
                          'block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                          passwordErrors.confirmPassword ? 'border-red-300' : ''
                        ]"
                        placeholder="请再次输入新密码"
                      />
                      <button
                        type="button"
                        @click="showConfirmPassword = !showConfirmPassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        <EyeIcon v-if="!showConfirmPassword" class="h-5 w-5 text-gray-400" />
                        <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                    <p v-if="passwordErrors.confirmPassword" class="mt-1 text-sm text-red-600">
                      {{ passwordErrors.confirmPassword }}
                    </p>
                  </div>
                </form>
              </div>

              <!-- 第二步：邮箱验证 -->
              <div v-if="passwordStep === 2">
                <h4 class="text-sm font-medium text-gray-900 mb-4">邮箱验证</h4>
                <div class="text-center">
                  <EnvelopeIcon class="mx-auto h-12 w-12 text-primary-600 mb-4" />
                  <p class="text-sm text-gray-600 mb-4">
                    我们已向您的邮箱 <span class="font-medium">{{ maskedEmail }}</span> 发送了验证码
                  </p>
                  <p class="text-xs text-gray-500 mb-6">请查收邮件并输入6位验证码</p>

                  <div class="space-y-4">
                    <div>
                      <label for="verificationCode" class="block text-sm font-medium text-gray-700">验证码</label>
                      <input
                        id="verificationCode"
                        v-model="passwordForm.verificationCode"
                        type="text"
                        maxlength="6"
                        :class="[
                          'mt-1 block w-full text-center text-lg tracking-widest rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                          passwordErrors.verificationCode ? 'border-red-300' : ''
                        ]"
                        placeholder="请输入验证码"
                      />
                      <p v-if="passwordErrors.verificationCode" class="mt-1 text-sm text-red-600">
                        {{ passwordErrors.verificationCode }}
                      </p>
                    </div>

                    <div class="text-center">
                      <button
                        v-if="verificationCountdown > 0"
                        disabled
                        class="text-sm text-gray-400"
                      >
                        {{ verificationCountdown }}秒后可重新发送
                      </button>
                      <button
                        v-else
                        @click="resendVerificationCode"
                        :disabled="emailVerificationLoading"
                        class="text-sm text-primary-600 hover:text-primary-500"
                      >
                        {{ emailVerificationLoading ? '发送中...' : '重新发送验证码' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三步：完成 -->
              <div v-if="passwordStep === 3">
                <div class="text-center">
                  <CheckCircleIcon class="mx-auto h-12 w-12 text-green-600 mb-4" />
                  <h4 class="text-lg font-medium text-gray-900 mb-2">密码修改成功！</h4>
                  <p class="text-sm text-gray-600 mb-4">您的密码已成功更新</p>
                  <p class="text-xs text-gray-500">窗口将在3秒后自动关闭</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div v-if="passwordStep < 3" class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              v-if="passwordStep === 1"
              @click="submitPasswordChange"
              :disabled="passwordLoading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              {{ passwordLoading ? '验证中...' : '下一步' }}
            </button>

            <button
              v-if="passwordStep === 2"
              @click="verifyCodeAndChangePassword"
              :disabled="passwordLoading || !passwordForm.verificationCode"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              {{ passwordLoading ? '修改中...' : '确认修改' }}
            </button>

            <button
              @click="closePasswordModal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 安全设置页面样式 */
</style>
