<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useToast } from 'vue-toastification'
import { 
  EnvelopeIcon,
  ArrowLeftIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'
import { authApi } from '@/api/auth'

const toast = useToast()

// 表单数据
const form = reactive({
  email: ''
})

// 表单状态
const loading = ref(false)
const emailSent = ref(false)
const errors = ref<Record<string, string[]>>({})

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string[]> = {}

  // 邮箱验证
  if (!form.email.trim()) {
    newErrors.email = ['邮箱不能为空']
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = ['请输入有效的邮箱地址']
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 计算属性
const isFormValid = computed(() => {
  return form.email && Object.keys(errors.value).length === 0
})

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true
    const response = await authApi.forgotPassword(form.email)
    
    if (response.success) {
      emailSent.value = true
      toast.success('密码重置邮件已发送，请查收邮箱')
    } else {
      toast.error(response.message || '发送失败，请稍后重试')
    }
  } catch (error: any) {
    console.error('Forgot password error:', error)
    
    // 处理服务器验证错误
    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message)
    } else {
      toast.error('发送失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 重新发送邮件
const resendEmail = () => {
  emailSent.value = false
  form.email = ''
  errors.value = {}
}

// 清除字段错误
const clearFieldError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div>
        <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">API</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          {{ emailSent ? '邮件已发送' : '重置密码' }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ emailSent ? '请查收您的邮箱' : '输入您的邮箱地址，我们将发送重置链接' }}
        </p>
      </div>

      <!-- 成功状态 -->
      <div v-if="emailSent" class="bg-white rounded-lg shadow p-8">
        <div class="text-center">
          <CheckCircleIcon class="mx-auto h-16 w-16 text-green-500 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">邮件发送成功</h3>
          <p class="text-gray-600 mb-6">
            我们已向 <strong>{{ form.email }}</strong> 发送了密码重置邮件。
            请查收邮箱并点击邮件中的链接来重置您的密码。
          </p>
          
          <div class="space-y-4">
            <div class="text-sm text-gray-500">
              <p>没有收到邮件？</p>
              <ul class="mt-2 space-y-1">
                <li>• 请检查垃圾邮件文件夹</li>
                <li>• 确认邮箱地址是否正确</li>
                <li>• 邮件可能需要几分钟才能到达</li>
              </ul>
            </div>
            
            <div class="flex space-x-4">
              <button
                @click="resendEmail"
                class="flex-1 btn-outline"
              >
                重新发送
              </button>
              <RouterLink
                to="/auth/login"
                class="flex-1 btn-primary text-center"
              >
                返回登录
              </RouterLink>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单状态 -->
      <form v-else class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="bg-white rounded-lg shadow p-8 space-y-6">
          <!-- 邮箱 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
              邮箱地址
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <EnvelopeIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="email"
                v-model="form.email"
                type="email"
                autocomplete="email"
                required
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                :class="{ 'border-red-300 focus:ring-red-500 focus:border-red-500': errors.email }"
                placeholder="请输入您的邮箱地址"
                @input="clearFieldError('email')"
              />
            </div>
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email[0] }}</p>
            <p class="mt-1 text-xs text-gray-500">
              我们将向此邮箱发送密码重置链接
            </p>
          </div>

          <!-- 提交按钮 -->
          <div>
            <button
              type="submit"
              :disabled="loading || !isFormValid"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? '发送中...' : '发送重置邮件' }}
            </button>
          </div>
        </div>
      </form>

      <!-- 返回链接 -->
      <div class="text-center">
        <RouterLink
          to="/auth/login"
          class="inline-flex items-center text-sm text-primary-600 hover:text-primary-500"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-1" />
          返回登录页面
        </RouterLink>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 text-gray-500">需要帮助？</span>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            如果您在重置密码时遇到问题，请
            <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
              联系客服
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-outline {
  @apply border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}
</style>
