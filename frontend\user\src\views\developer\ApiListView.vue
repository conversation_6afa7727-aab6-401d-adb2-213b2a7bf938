<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  BookOpenIcon,
  CodeBracketIcon,
  ClockIcon,
  TagIcon
} from '@heroicons/vue/24/outline'
import { apisApi } from '@/api/apis'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
import type { ApiEndpoint } from '@/types'
const apis = ref<ApiEndpoint[]>([])
const categories = ref<Array<{ id: string; name: string; count: number }>>([])
const loading = ref(false)
const showAdvancedFilter = ref(false)

// 高级筛选选项
const selectedMethod = ref('all')
const selectedStatus = ref('all')
const sortBy = ref('updated_at')
const sortOrder = ref('desc')



// 生命周期
onMounted(() => {
  loadApis()
  loadCategories()
})

// 方法
const loadApis = async () => {
  loading.value = true
  try {
    // 调用真实API获取当前用户的接口列表
    const params = {
      keyword: searchQuery.value,
      category_id: selectedCategory.value === 'all' ? undefined : Number(selectedCategory.value),
      method: selectedMethod.value === 'all' ? undefined : selectedMethod.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value,
      sort_by: sortBy.value,
      sort_order: sortOrder.value,
      per_page: 20
    }

    const response = await apisApi.getUserApis(params)

    if (response.success) {
      apis.value = response.data.data || []
    } else {
      console.error('Failed to load APIs:', response.message)
      apis.value = []
    }
  } catch (error) {
    console.error('Failed to load APIs:', error)
    apis.value = []
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    // 模拟分类数据，实际应该从API获取
    categories.value = [
      { id: 'all', name: '全部', count: 0 },
      { id: 'auth', name: '认证', count: 0 },
      { id: 'data', name: '数据', count: 0 },
      { id: 'file', name: '文件', count: 0 },
      { id: 'payment', name: '支付', count: 0 },
      { id: 'notification', name: '通知', count: 0 },
      { id: 'other', name: '其他', count: 0 }
    ]
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

// 切换高级筛选
const toggleAdvancedFilter = () => {
  showAdvancedFilter.value = !showAdvancedFilter.value
}

// 应用筛选
const applyFilters = () => {
  loadApis()
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = 'all'
  selectedMethod.value = 'all'
  selectedStatus.value = 'all'
  sortBy.value = 'updated_at'
  sortOrder.value = 'desc'
  loadApis()
}

const filteredApis = computed(() => {
  let filtered = apis.value

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter((api: any) => String(api.category) === String(selectedCategory.value))
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((api: any) =>
      api.name.toLowerCase().includes(query) ||
      api.description.toLowerCase().includes(query) ||
      api.endpoint.toLowerCase().includes(query)
    )
  }

  return filtered
})

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800'
    case 'beta': return 'bg-yellow-100 text-yellow-800'
    case 'deprecated': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getMethodColor = (method: string) => {
  switch (method) {
    case 'GET': return 'bg-blue-100 text-blue-800'
    case 'POST': return 'bg-green-100 text-green-800'
    case 'PUT': return 'bg-yellow-100 text-yellow-800'
    case 'DELETE': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const viewApiDetail = (api: any) => {
  // 跳转到API详情页面
  router.push(`/developer/apis/${api.id}`)
}
</script>

<template>
  <div class="p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">接口列表</h1>
          <p class="mt-1 text-sm text-gray-500">浏览和查找可用的API接口</p>
        </div>
        <div class="flex space-x-3">
          <router-link
            to="/developer/submit"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <CodeBracketIcon class="h-4 w-4 mr-2" />
            提交接口
          </router-link>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- 侧边栏 - 分类筛选 -->
        <div class="lg:w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">分类筛选</h3>
            <div class="space-y-2">
              <button
                v-for="category in categories"
                :key="category.id"
                @click="selectedCategory = category.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  selectedCategory === category.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center justify-between">
                  <span>{{ category.name }}</span>
                  <span class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                    {{ category.count }}
                  </span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1">
          <!-- 搜索和筛选 -->
          <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <div class="relative">
                  <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="搜索API名称、描述或端点..."
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              <button
                @click="toggleAdvancedFilter"
                :class="[
                  'inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium transition-colors',
                  showAdvancedFilter
                    ? 'border-primary-500 text-primary-700 bg-primary-50'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                ]"
              >
                <FunnelIcon class="h-4 w-4 mr-2" />
                高级筛选
              </button>
            </div>

            <!-- 高级筛选面板 -->
            <div v-if="showAdvancedFilter" class="mt-4 p-4 bg-gray-50 rounded-lg border">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 请求方法筛选 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">请求方法</label>
                  <select
                    v-model="selectedMethod"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="all">全部方法</option>
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                </div>

                <!-- 状态筛选 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">接口状态</label>
                  <select
                    v-model="selectedStatus"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="all">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">停用</option>
                    <option value="beta">测试版</option>
                    <option value="deprecated">已弃用</option>
                  </select>
                </div>

                <!-- 排序字段 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">排序字段</label>
                  <select
                    v-model="sortBy"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="updated_at">更新时间</option>
                    <option value="created_at">创建时间</option>
                    <option value="name">接口名称</option>
                    <option value="call_count">调用次数</option>
                  </select>
                </div>

                <!-- 排序方向 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">排序方向</label>
                  <select
                    v-model="sortOrder"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="desc">降序</option>
                    <option value="asc">升序</option>
                  </select>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-4 flex justify-end space-x-3">
                <button
                  @click="resetFilters"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  重置
                </button>
                <button
                  @click="applyFilters"
                  class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
                >
                  应用筛选
                </button>
              </div>
            </div>
          </div>

          <!-- API列表 -->
          <div class="space-y-4">
            <div v-if="loading" class="text-center py-8">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p class="mt-2 text-sm text-gray-500">加载中...</p>
            </div>

            <div v-else-if="filteredApis.length === 0" class="text-center py-8">
              <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">暂无API接口</h3>
              <p class="mt-1 text-sm text-gray-500">尝试调整搜索条件或分类筛选</p>
            </div>

            <div
              v-else
              v-for="api in filteredApis"

              :key="api.id"
              class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
              @click="viewApiDetail(api)"
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-2">
                      <h3 class="text-lg font-medium text-gray-900">{{ api.name }}</h3>
                      <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusColor(String(api.status || ''))]">
                        {{ api.status }}
                      </span>
                      <span :class="['px-2 py-1 text-xs font-medium rounded-full', getMethodColor(String(api.method || ''))]">
                        {{ api.method }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">{{ api.description }}</p>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                      <div class="flex items-center">
                        <CodeBracketIcon class="h-4 w-4 mr-1" />
                        {{ api.endpoint }}
                      </div>
                      <div class="flex items-center">
                        <TagIcon class="h-4 w-4 mr-1" />
                        {{ api.version }}
                      </div>
                      <div class="flex items-center">
                        <ClockIcon class="h-4 w-4 mr-1" />
                        {{ api.lastUpdated }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<style scoped>
/* API列表页面样式 */
</style>
