<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { 
  HeartIcon as HeartOutlineIcon,
  StarIcon as StarOutlineIcon,
  EyeIcon,
  ClockIcon,
  ShieldCheckIcon,
  GlobeAltIcon
} from '@heroicons/vue/24/outline'
import { 
  HeartIcon as HeartSolidIcon,
  StarIcon as StarSolidIcon
} from '@heroicons/vue/24/solid'
import type { ApiEndpoint } from '@/types'

interface Props {
  api: ApiEndpoint
  isFavorited?: boolean
  showCategory?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isFavorited: false,
  showCategory: true
})

const emit = defineEmits<{
  favorite: [apiId: number]
  unfavorite: [apiId: number]
}>()

// HTTP方法颜色映射
const methodColors = {
  GET: 'bg-green-100 text-green-800 border-green-200',
  POST: 'bg-blue-100 text-blue-800 border-blue-200',
  PUT: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  DELETE: 'bg-red-100 text-red-800 border-red-200',
  PATCH: 'bg-purple-100 text-purple-800 border-purple-200'
}

const methodColor = computed(() => (methodColors as any)[props.api.method] || 'bg-gray-100 text-gray-800 border-gray-200')

const handleFavoriteToggle = () => {
  if (props.isFavorited) {
    emit('unfavorite', props.api.id)
  } else {
    emit('favorite', props.api.id)
  }
}

// 模拟评分数据（实际应该从API获取）
const averageRating = computed(() => 4.2)
const totalRatings = computed(() => 156)
</script>

<template>
  <div class="card hover:shadow-md transition-shadow duration-200">
    <div class="card-body">
      <!-- 头部信息 -->
      <div class="flex items-start justify-between mb-4">
        <div class="flex-1">
          <div class="flex items-center gap-2 mb-2">
            <span 
              :class="methodColor"
              class="px-2 py-1 text-xs font-semibold rounded border"
            >
              {{ api.method }}
            </span>
            <span v-if="api.is_public" class="flex items-center text-xs text-green-600">
              <GlobeAltIcon class="w-3 h-3 mr-1" />
              公开
            </span>
            <span v-if="api.requires_auth" class="flex items-center text-xs text-blue-600">
              <ShieldCheckIcon class="w-3 h-3 mr-1" />
              需要认证
            </span>
          </div>
          
          <RouterLink 
            :to="`/apis/${api.id}`"
            class="block group"
          >
            <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
              {{ api.name }}
            </h3>
          </RouterLink>
          
          <p class="text-sm text-gray-600 mt-1 line-clamp-2">
            {{ api.description }}
          </p>
        </div>
        
        <!-- 收藏按钮 -->
        <button
          @click="handleFavoriteToggle"
          class="ml-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
          :class="{ 'text-red-500': isFavorited, 'text-gray-400': !isFavorited }"
        >
          <HeartSolidIcon v-if="isFavorited" class="w-5 h-5" />
          <HeartOutlineIcon v-else class="w-5 h-5" />
        </button>
      </div>

      <!-- API端点 -->
      <div class="mb-4">
        <code class="text-sm bg-gray-100 px-2 py-1 rounded text-gray-800 break-all">
          {{ api.endpoint }}
        </code>
      </div>

      <!-- 分类信息 -->
      <div v-if="showCategory && api.category" class="mb-4">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
          {{ api.category.name }}
        </span>
      </div>

      <!-- 底部信息 -->
      <div class="flex items-center justify-between text-sm text-gray-500">
        <div class="flex items-center space-x-4">
          <!-- 评分 -->
          <div class="flex items-center">
            <StarSolidIcon class="w-4 h-4 text-yellow-400 mr-1" />
            <span class="font-medium">{{ averageRating }}</span>
            <span class="ml-1">({{ totalRatings }})</span>
          </div>
          
          <!-- 版本 -->
          <div class="flex items-center">
            <span class="text-xs bg-gray-200 px-2 py-1 rounded">
              v{{ api.version }}
            </span>
          </div>
        </div>
        
        <!-- 创建时间 -->
        <div class="flex items-center">
          <ClockIcon class="w-4 h-4 mr-1" />
          <span>{{ new Date(api.created_at).toLocaleDateString() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
