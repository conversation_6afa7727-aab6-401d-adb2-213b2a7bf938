<!-- Copyright (c) 2025 Aoki. All rights reserved. -->

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import {
  DocumentTextIcon,
  CodeBracketIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { userApi } from '@/api/user'

const router = useRouter()
const toast = useToast()

// 表单数据
const form = reactive<{ name: string; description: string; author: string; category: string; method: string; endpoint: string; url: string; version: string; documentation: string; example_request: string; example_response: string; response_format: string; parameters: Array<{ name: string; type: string; required: boolean; description: string }>; responses: Array<{ code: string; description: string; example: string }>; tags: string[] }>({
  name: '',
  description: '',
  author: '',
  category: '',
  method: 'GET',
  endpoint: '',
  url: '',
  version: '1.0.0',
  documentation: '',
  example_request: '',
  example_response: '',
  response_format: 'JSON',
  parameters: [],
  responses: [],
  tags: []
})

// 状态
const loading = ref(false)
const errors = ref<Record<string, string>>({})

// 选项数据
const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
const categories = [
  { value: 'auth', label: '认证' },
  { value: 'data', label: '数据' },
  { value: 'file', label: '文件' },
  { value: 'payment', label: '支付' },
  { value: 'notification', label: '通知' },
  { value: 'other', label: '其他' }
]
const responseFormats = [
  { value: 'JSON', label: 'JSON' },
  { value: 'XML', label: 'XML' },
  { value: 'TEXT', label: 'TEXT' },
  { value: 'HTML', label: 'HTML' },
  { value: 'BINARY', label: 'BINARY' }
]

// 参数管理
const addParameter = () => {
  form.parameters.push({
    name: '',
    type: 'string',
    required: true,
    description: ''
  } as any)
}

const removeParameter = (index: number) => {
  form.parameters.splice(index, 1)
}

// 响应管理
const addResponse = () => {
  form.responses.push({
    code: '200',
    description: '',
    example: ''
  } as any)
}

const removeResponse = (index: number) => {
  form.responses.splice(index, 1)
}

// 标签管理
const tagInput = ref('')
const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !form.tags.includes(tag)) {
    form.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string> = {}

  if (!form.name.trim()) {
    newErrors.name = '接口名称不能为空'
  }

  if (!form.description.trim()) {
    newErrors.description = '接口描述不能为空'
  }

  if (!form.author.trim()) {
    newErrors.author = '接口作者不能为空'
  }

  if (!form.category) {
    newErrors.category = '请选择接口分类'
  }

  if (!form.endpoint.trim()) {
    newErrors.endpoint = '接口地址不能为空'
  } else if (!form.endpoint.startsWith('/')) {
    newErrors.endpoint = '接口地址必须以 / 开头'
  }

  if (!form.url.trim()) {
    newErrors.url = '完整接口地址不能为空'
  } else if (!form.url.startsWith('http')) {
    newErrors.url = '完整接口地址必须以 http 或 https 开头'
  }

  if (!form.version.trim()) {
    newErrors.version = '版本号不能为空'
  }

  if (!form.example_request.trim()) {
    newErrors.example_request = '请求示例不能为空'
  }

  if (!form.example_response.trim()) {
    newErrors.example_response = '返回示例不能为空'
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 提交表单
const submitForm = async () => {
  if (!validateForm()) {
    toast.error('请检查表单中的错误')
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    toast.success('接口提交成功，等待审核')
    router.push('/developer/apis')
  } catch (error) {
    console.error('Submit error:', error)
    toast.error('提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 保存草稿
const saveDraft = async () => {
  try {
    // 保存到本地存储
    localStorage.setItem('api_draft', JSON.stringify(form))
    toast.success('草稿已保存')
  } catch (error) {
    console.error('Save draft error:', error)
    toast.error('保存草稿失败')
  }
}

// 加载草稿
const loadDraft = () => {
  try {
    const draft = localStorage.getItem('api_draft')
    if (draft) {
      const draftData = JSON.parse(draft)
      Object.assign(form, draftData)
      toast.info('已加载草稿数据')
    }
  } catch (error) {
    console.error('Load draft error:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    author: '',
    category: '',
    method: 'GET',
    endpoint: '',
    url: '',
    version: '1.0.0',
    documentation: '',
    example_request: '',
    example_response: '',
    response_format: 'JSON',
    parameters: [],
    responses: [],
    tags: []
  })
  errors.value = {}
  // 清除草稿
  localStorage.removeItem('api_draft')
  // 重新设置默认作者
  loadUserInfo()
}

// 获取用户信息并设置默认作者
const loadUserInfo = async () => {
  try {
    const response = await userApi.getProfile()
    if (response.success && response.data) {
      form.author = response.data.name || ''
    }
  } catch (error) {
    console.error('Failed to load user info:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  loadUserInfo()
})

// 初始化一些默认数据
addParameter()
addResponse()
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面头部 -->
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">提交API接口</h1>
            <p class="mt-2 text-lg text-gray-600">分享您的API接口，让更多开发者受益</p>
            <div class="mt-4 flex items-center space-x-4 text-sm text-gray-500">
              <span class="flex items-center">
                <CheckCircleIcon class="h-4 w-4 mr-1 text-green-500" />
                免费提交
              </span>
              <span class="flex items-center">
                <CheckCircleIcon class="h-4 w-4 mr-1 text-green-500" />
                快速审核
              </span>
              <span class="flex items-center">
                <CheckCircleIcon class="h-4 w-4 mr-1 text-green-500" />
                获得收益
              </span>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row gap-3">
            <router-link
              to="/developer/apis"
              class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              返回列表
            </router-link>
            <button
              type="button"
              @click="saveDraft"
              class="inline-flex items-center justify-center px-4 py-2 border border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              保存草稿
            </button>
          </div>
        </div>
      </div>

      <!-- 进度指示器 -->
      <div class="mb-8">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
                  1
                </div>
                <span class="ml-2 text-sm font-medium text-gray-900">基本信息</span>
              </div>
              <div class="w-16 h-1 bg-gray-200 rounded"></div>
              <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full text-sm font-medium">
                  2
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500">接口配置</span>
              </div>
              <div class="w-16 h-1 bg-gray-200 rounded"></div>
              <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full text-sm font-medium">
                  3
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500">示例文档</span>
              </div>
            </div>
            <div class="text-sm text-gray-500">
              预计完成时间：5-10分钟
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 左侧表单 -->
        <div class="lg:col-span-3">
          <form @submit.prevent="submitForm" class="space-y-8">
            <!-- 基本信息 -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200">
              <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                  <InformationCircleIcon class="h-6 w-6 mr-3 text-blue-600" />
                  基本信息
                </h3>
                <p class="mt-2 text-sm text-gray-600">填写API的基本信息，让其他开发者快速了解您的接口</p>
              </div>
              <div class="px-8 py-8">
                <!-- 接口名称和作者 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                  <div class="space-y-2">
                    <label for="name" class="block text-sm font-semibold text-gray-900">
                      接口名称 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="name"
                      v-model="form.name"
                      type="text"
                      :class="[
                        'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                        errors.name
                          ? 'border-red-300 focus:border-red-500 bg-red-50'
                          : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                      ]"
                      placeholder="例如：用户认证API"
                    />
                    <p v-if="errors.name" class="text-sm text-red-600 flex items-center">
                      <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                      {{ errors.name }}
                    </p>
                    <p class="text-xs text-gray-500">给您的API起一个简洁明了的名称</p>
                  </div>

                  <div class="space-y-2">
                    <label for="author" class="block text-sm font-semibold text-gray-900">
                      接口作者 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="author"
                      v-model="form.author"
                      type="text"
                      :class="[
                        'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                        errors.author
                          ? 'border-red-300 focus:border-red-500 bg-red-50'
                          : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                      ]"
                      placeholder="例如：张三"
                    />
                    <p v-if="errors.author" class="text-sm text-red-600 flex items-center">
                      <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                      {{ errors.author }}
                    </p>
                    <p class="text-xs text-gray-500">您的姓名或团队名称</p>
                  </div>
                </div>

                <!-- 分类选择 -->
                <div class="mb-8">
                  <label for="category" class="block text-sm font-semibold text-gray-900 mb-3">
                    接口分类 <span class="text-red-500">*</span>
                  </label>
                  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <div
                      v-for="cat in categories"
                      :key="cat.value"
                      @click="form.category = cat.value"
                      :class="[
                        'relative cursor-pointer rounded-lg border-2 p-4 text-center transition-all duration-200',
                        form.category === cat.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      ]"
                    >
                      <div class="text-sm font-medium">{{ cat.label }}</div>
                      <CheckCircleIcon
                        v-if="form.category === cat.value"
                        class="absolute top-2 right-2 h-4 w-4 text-blue-600"
                      />
                    </div>
                  </div>
                  <p v-if="errors.category" class="mt-2 text-sm text-red-600 flex items-center">
                    <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                    {{ errors.category }}
                  </p>
                  <p class="mt-2 text-xs text-gray-500">选择最符合您API功能的分类</p>
                </div>

                <!-- 接口描述 -->
                <div class="space-y-2">
                  <label for="description" class="block text-sm font-semibold text-gray-900">
                    接口描述 <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="description"
                    v-model="form.description"
                    rows="4"
                    :class="[
                      'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0 resize-none',
                      errors.description
                        ? 'border-red-300 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                    ]"
                    placeholder="详细描述接口的功能、用途、适用场景等..."
                  ></textarea>
                  <p v-if="errors.description" class="text-sm text-red-600 flex items-center">
                    <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                    {{ errors.description }}
                  </p>
                  <p class="text-xs text-gray-500">详细的描述有助于其他开发者理解和使用您的API</p>
                </div>
              </div>
            </div>

            <!-- 技术配置 -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200">
              <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                  <CodeBracketIcon class="h-6 w-6 mr-3 text-green-600" />
                  技术配置
                </h3>
                <p class="mt-2 text-sm text-gray-600">配置API的技术参数和访问信息</p>
              </div>
              <div class="px-8 py-8">
                <!-- 请求方法和版本 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  <div class="space-y-2">
                    <label for="method" class="block text-sm font-semibold text-gray-900">请求方法</label>
                    <div class="grid grid-cols-2 gap-2">
                      <button
                        v-for="method in methods"
                        :key="method"
                        type="button"
                        @click="form.method = method"
                        :class="[
                          'px-3 py-2 text-sm font-medium rounded-lg border-2 transition-all duration-200',
                          form.method === method
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        ]"
                      >
                        {{ method }}
                      </button>
                    </div>
                    <p class="text-xs text-gray-500">选择API的HTTP请求方法</p>
                  </div>

                  <div class="space-y-2">
                    <label for="endpoint" class="block text-sm font-semibold text-gray-900">
                      接口路径 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="endpoint"
                      v-model="form.endpoint"
                      type="text"
                      :class="[
                        'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                        errors.endpoint
                          ? 'border-red-300 focus:border-red-500 bg-red-50'
                          : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                      ]"
                      placeholder="/api/users"
                    />
                    <p v-if="errors.endpoint" class="text-sm text-red-600 flex items-center">
                      <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                      {{ errors.endpoint }}
                    </p>
                    <p class="text-xs text-gray-500">API的相对路径，如 /api/users</p>
                  </div>

                  <div class="space-y-2">
                    <label for="version" class="block text-sm font-semibold text-gray-900">
                      版本号 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="version"
                      v-model="form.version"
                      type="text"
                      :class="[
                        'block w-full px-4 py-3 rounded-lg border-2 transition-colors duration-200 focus:outline-none focus:ring-0',
                        errors.version
                          ? 'border-red-300 focus:border-red-500 bg-red-50'
                          : 'border-gray-200 focus:border-blue-500 hover:border-gray-300'
                      ]"
                      placeholder="1.0.0"
                    />
                    <p v-if="errors.version" class="text-sm text-red-600 flex items-center">
                      <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                      {{ errors.version }}
                    </p>
                    <p class="text-xs text-gray-500">遵循语义化版本规范</p>
                  </div>
                </div>

            <!-- 完整接口地址 -->
            <div class="mt-6">
              <label for="url" class="block text-sm font-medium text-gray-700">完整接口地址 *</label>
              <input
                id="url"
                v-model="form.url"
                type="url"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.url ? 'border-red-300' : ''
                ]"
                placeholder="https://api.example.com/v1/users"
              />
              <p v-if="errors.url" class="mt-1 text-sm text-red-600">{{ errors.url }}</p>
              <p class="mt-1 text-sm text-gray-500">API的完整访问地址，包含域名和协议</p>
            </div>
          </div>
        </div>

        <!-- 参数配置 -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <CodeBracketIcon class="h-5 w-5 mr-2" />
                请求参数
              </h3>
              <button
                type="button"
                @click="addParameter"
                class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
              >
                添加参数
              </button>
            </div>
          </div>
          <div class="px-6 py-6">
            <div v-if="form.parameters.length === 0" class="text-center py-4 text-gray-500">
              暂无参数，点击"添加参数"开始配置
            </div>
            <div v-else class="space-y-4">
              <div
                v-for="(param, index) in form.parameters"
                :key="index"
                class="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border border-gray-200 rounded-lg"
              >
                <div>
                  <label class="block text-sm font-medium text-gray-700">参数名</label>
                  <input
                    v-model="param.name"
                    type="text"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    placeholder="参数名"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">类型</label>
                  <select
                    v-model="param.type"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="string">字符串</option>
                    <option value="number">数字</option>
                    <option value="boolean">布尔值</option>
                    <option value="array">数组</option>
                    <option value="object">对象</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">是否必需</label>
                  <select
                    v-model="param.required"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option :value="true">必需</option>
                    <option :value="false">可选</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button
                    type="button"
                    @click="removeParameter(index)"
                    class="w-full px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                  >
                    删除
                  </button>
                </div>
                <div class="md:col-span-4">
                  <label class="block text-sm font-medium text-gray-700">参数描述</label>
                  <input
                    v-model="param.description"
                    type="text"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    placeholder="参数的详细描述..."
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 示例和格式 -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <CodeBracketIcon class="h-5 w-5 mr-2 text-gray-400" />
              示例和格式
            </h3>
          </div>
          <div class="p-6 space-y-6">
            <!-- 请求示例 -->
            <div>
              <label for="example_request" class="block text-sm font-medium text-gray-700">请求示例 *</label>
              <textarea
                id="example_request"
                v-model="form.example_request"
                rows="6"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.example_request ? 'border-red-300' : ''
                ]"
                placeholder="curl -X POST https://api.example.com/v1/users \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -d '{
    &quot;name&quot;: &quot;张三&quot;,
    &quot;email&quot;: &quot;<EMAIL>&quot;
  }'"
              ></textarea>
              <p v-if="errors.example_request" class="mt-1 text-sm text-red-600">{{ errors.example_request }}</p>
              <p class="mt-1 text-sm text-gray-500">展示如何调用该接口的示例代码</p>
            </div>

            <!-- 返回示例 -->
            <div>
              <label for="example_response" class="block text-sm font-medium text-gray-700">返回示例 *</label>
              <textarea
                id="example_response"
                v-model="form.example_response"
                rows="8"
                :class="[
                  'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                  errors.example_response ? 'border-red-300' : ''
                ]"
                placeholder="{
  &quot;success&quot;: true,
  &quot;data&quot;: {
    &quot;id&quot;: 1,
    &quot;name&quot;: &quot;张三&quot;,
    &quot;email&quot;: &quot;<EMAIL>&quot;,
    &quot;created_at&quot;: &quot;2025-01-15T10:30:00Z&quot;
  },
  &quot;message&quot;: &quot;用户创建成功&quot;
}"
              ></textarea>
              <p v-if="errors.example_response" class="mt-1 text-sm text-red-600">{{ errors.example_response }}</p>
              <p class="mt-1 text-sm text-gray-500">API响应的示例数据</p>
            </div>

            <!-- 返回格式 -->
            <div>
              <label for="response_format" class="block text-sm font-medium text-gray-700">返回格式</label>
              <select
                id="response_format"
                v-model="form.response_format"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option v-for="format in responseFormats" :key="format.value" :value="format.value">
                  {{ format.label }}
                </option>
              </select>
              <p class="mt-1 text-sm text-gray-500">响应数据的格式说明</p>
            </div>
          </div>
        </div>

            <!-- 提交按钮 -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 p-8">
              <div class="flex flex-col sm:flex-row gap-4">
                <button
                  type="button"
                  @click="resetForm"
                  class="flex-1 px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                >
                  重置表单
                </button>
                <button
                  type="button"
                  @click="saveDraft"
                  class="flex-1 px-6 py-3 border-2 border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 hover:border-blue-400 transition-all duration-200"
                >
                  保存草稿
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="flex-1 px-6 py-3 border-2 border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  <span v-if="loading" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    提交中...
                  </span>
                  <span v-else>🚀 提交接口</span>
                </button>
              </div>
            </div>
          </form>
        </div>

        <!-- 右侧帮助面板 -->
        <div class="lg:col-span-1">
          <div class="sticky top-8 space-y-6">
            <!-- 提交指南 -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                  <DocumentTextIcon class="h-5 w-5 mr-2 text-blue-600" />
                  提交指南
                </h3>
              </div>
              <div class="px-6 py-4 space-y-4">
                <div class="space-y-3">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-blue-600">1</span>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">填写基本信息</h4>
                      <p class="text-xs text-gray-600">提供清晰的接口名称和详细描述</p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-blue-600">2</span>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">配置技术参数</h4>
                      <p class="text-xs text-gray-600">设置正确的请求方法和接口路径</p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-blue-600">3</span>
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">提供示例代码</h4>
                      <p class="text-xs text-gray-600">添加请求和响应示例帮助理解</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审核说明 -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
              <h3 class="text-lg font-semibold text-green-900 mb-3">审核说明</h3>
              <div class="space-y-2 text-sm text-green-800">
                <p>• 审核时间：1-3个工作日</p>
                <p>• 审核标准：功能完整、文档清晰</p>
                <p>• 通过后即可在平台展示</p>
                <p>• 可获得调用收益分成</p>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 提交接口页面样式 */
</style>
